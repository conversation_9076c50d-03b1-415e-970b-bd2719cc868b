import tkinter as tk
from tkinter import simpledialog, messagebox, ttk
import webbrowser
import json
import os
import pyperclip

class URLManager:
    def __init__(self, master):
        self.master = master
        self.urls = {}
        self.file_path = "urls.json"
        
        self.load_urls()
        
        self.add_url_button = tk.Button(master, text="Add URL", command=self.add_url)
        self.add_url_button.pack(pady=10)
        
        self.selection_frame = tk.Frame(master)
        self.selection_frame.pack(pady=10)

        self.url_name_var = tk.StringVar()
        self.url_selector = ttk.Combobox(self.selection_frame, textvariable=self.url_name_var)
        self.url_selector.pack(side=tk.LEFT, padx=5)

        self.edit_url_button = tk.Button(self.selection_frame, text="Edit URL", command=self.edit_url)
        self.edit_url_button.pack(side=tk.LEFT, padx=5)

        self.remove_url_button = tk.But<PERSON>(self.selection_frame, text="Remove URL", command=self.remove_url)
        self.remove_url_button.pack(side=tk.LEFT, padx=5)
        
        self.url_frame = tk.Frame(master)
        self.url_frame.pack(pady=10)
        
        self.update_url_buttons()
        self.update_url_selector()

    def load_urls(self):
        if os.path.exists(self.file_path):
            with open(self.file_path, 'r') as file:
                self.urls = json.load(file)

    def save_urls(self):
        with open(self.file_path, 'w') as file:
            json.dump(self.urls, file)

    def add_url(self):
        name = simpledialog.askstring("Input", "Enter the name:")
        if not name:
            return
        url = simpledialog.askstring("Input", "Enter the URL:")
        if not url:
            return
        
        if name in self.urls:
            messagebox.showerror("Error", "Name already exists!")
        else:
            self.urls[name] = url
            self.save_urls()
            self.update_url_buttons()
            self.update_url_selector()

    def open_url(self, url):
        chrome_path = 'C:/Program Files/Google/Chrome/Application/chrome.exe %s'
        webbrowser.get(chrome_path).open(url)

    def edit_url(self):
        name = self.url_name_var.get()
        if name in self.urls:
            new_url = simpledialog.askstring("Input", f"Enter the new URL for {name}:")
            if new_url:
                self.urls[name] = new_url
                self.save_urls()
                self.update_url_buttons()
        else:
            messagebox.showerror("Error", "No URL selected or URL does not exist!")

    def remove_url(self):
        name = self.url_name_var.get()
        if name in self.urls:
            if messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete {name}?"):
                del self.urls[name]
                self.save_urls()
                self.update_url_buttons()
                self.update_url_selector()
        else:
            messagebox.showerror("Error", "No URL selected or URL does not exist!")

    def update_url_buttons(self):
        for widget in self.url_frame.winfo_children():
            widget.destroy()
        
        row, col = 0, 0
        for name, url in self.urls.items():
            frame = tk.Frame(self.url_frame)
            frame.grid(row=row, column=col, padx=5, pady=5)

            button = tk.Button(frame, text=name, command=lambda url=url: self.open_url(url))
            button.pack(side=tk.LEFT)

            col += 1
            if col == 4:
                col = 0
                row += 1

    def update_url_selector(self):
        self.url_selector['values'] = list(self.urls.keys())
        self.url_selector.set('')


class CredentialManager:
    def __init__(self, master):
        self.master = master
        self.credentials = {}
        self.file_path = "credentials.json"
        
        self.load_credentials()
        
        self.add_credential_button = tk.Button(master, text="Add Credential", command=self.add_credential)
        self.add_credential_button.pack(pady=10)
        
        self.selection_frame = tk.Frame(master)
        self.selection_frame.pack(pady=10)

        self.title_var = tk.StringVar()
        self.title_selector = ttk.Combobox(self.selection_frame, textvariable=self.title_var)
        self.title_selector.pack(side=tk.LEFT, padx=5)

        self.edit_credential_button = tk.Button(self.selection_frame, text="Edit Credential", command=self.edit_credential)
        self.edit_credential_button.pack(side=tk.LEFT, padx=5)

        self.remove_credential_button = tk.Button(self.selection_frame, text="Remove Credential", command=self.remove_credential)
        self.remove_credential_button.pack(side=tk.LEFT, padx=5)

        self.copy_username_button = tk.Button(self.selection_frame, text="Copy Username", command=self.copy_username)
        self.copy_username_button.pack(side=tk.LEFT, padx=5)

        self.copy_password_button = tk.Button(self.selection_frame, text="Copy Password", command=self.copy_password)
        self.copy_password_button.pack(side=tk.LEFT, padx=5)
        
        self.credential_frame = tk.Frame(master)
        self.credential_frame.pack(pady=10)
        
        self.update_credential_buttons()
        self.update_title_selector()

    def load_credentials(self):
        if os.path.exists(self.file_path):
            with open(self.file_path, 'r') as file:
                self.credentials = json.load(file)

    def save_credentials(self):
        with open(self.file_path, 'w') as file:
            json.dump(self.credentials, file)

    def add_credential(self):
        title = simpledialog.askstring("Input", "Enter the title:")
        if not title:
            return
        username = simpledialog.askstring("Input", "Enter the username:")
        if not username:
            return
        password = simpledialog.askstring("Input", "Enter the password:")
        if not password:
            return
        
        if title in self.credentials:
            messagebox.showerror("Error", "Title already exists!")
        else:
            self.credentials[title] = {"username": username, "password": password}
            self.save_credentials()
            self.update_credential_buttons()
            self.update_title_selector()

    def edit_credential(self):
        title = self.title_var.get()
        if title in self.credentials:
            username = simpledialog.askstring("Input", f"Enter the new username for {title}:")
            if not username:
                return
            password = simpledialog.askstring("Input", f"Enter the new password for {title}:")
            if not password:
                return
            self.credentials[title] = {"username": username, "password": password}
            self.save_credentials()
            self.update_credential_buttons()
        else:
            messagebox.showerror("Error", "No credential selected or credential does not exist!")

    def remove_credential(self):
        title = self.title_var.get()
        if title in self.credentials:
            if messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete {title}?"):
                del self.credentials[title]
                self.save_credentials()
                self.update_credential_buttons()
                self.update_title_selector()
        else:
            messagebox.showerror("Error", "No credential selected or credential does not exist!")

    def copy_username(self):
        title = self.title_var.get()
        if title in self.credentials:
            pyperclip.copy(self.credentials[title]['username'])
            messagebox.showinfo("Username Copied", f"Username for {title} copied to clipboard.")
        else:
            messagebox.showerror("Error", "No credential selected or credential does not exist!")

    def copy_password(self):
        title = self.title_var.get()
        if title in self.credentials:
            pyperclip.copy(self.credentials[title]['password'])
            messagebox.showinfo("Password Copied", f"Password for {title} copied to clipboard.")
        else:
            messagebox.showerror("Error", "No credential selected or credential does not exist!")

    def update_credential_buttons(self):
        for widget in self.credential_frame.winfo_children():
            widget.destroy()
        
        row, col = 0, 0
        for title, cred in self.credentials.items():
            frame = tk.Frame(self.credential_frame)
            frame.grid(row=row, column=col, padx=5, pady=5)

            title_label = tk.Label(frame, text=title)
            title_label.pack(side=tk.TOP)

            username_label = tk.Label(frame, text=f"Username: {cred['username']}")
            username_label.pack(side=tk.TOP)

            col += 1
            if col == 2:  # 2 credentials per row for better visibility
                col = 0
                row += 1

    def update_title_selector(self):
        self.title_selector['values'] = list(self.credentials.keys())
        self.title_selector.set('')


class App:
    def __init__(self, master):
        self.master = master
        self.master.title("Manager Application")

        self.notebook = ttk.Notebook(master)
        self.notebook.pack(expand=1, fill='both')

        self.url_frame = tk.Frame(self.notebook)
        self.credential_frame = tk.Frame(self.notebook)

        self.url_manager = URLManager(self.url_frame)
        self.credential_manager = CredentialManager(self.credential_frame)

        self.notebook.add(self.url_frame, text='URL Manager')
        self.notebook.add(self.credential_frame, text='Credential Manager')

if __name__ == "__main__":
    root = tk.Tk()
    app = App(root)
    root.mainloop()
