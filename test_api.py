import requests
import json

# Base URL for the API
BASE_URL = "http://localhost:5000"

def test_api():
    """Test all API endpoints"""
    print("🚀 Testing REST API Endpoints\n")
    
    # Test 1: Get welcome message
    print("1. Testing Welcome Endpoint (GET /)")
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}\n")
    except requests.exceptions.ConnectionError:
        print("❌ Error: Could not connect to the API. Make sure the server is running.\n")
        return
    
    # Test 2: Get all users
    print("2. Testing Get All Users (GET /users)")
    response = requests.get(f"{BASE_URL}/users")
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}\n")
    
    # Test 3: Get specific user
    print("3. Testing Get User by ID (GET /users/1)")
    response = requests.get(f"{BASE_URL}/users/1")
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}\n")
    
    # Test 4: Create new user
    print("4. Testing Create User (POST /users)")
    new_user = {
        "name": "Alice <PERSON>",
        "email": "<EMAIL>"
    }
    response = requests.post(f"{BASE_URL}/users", json=new_user)
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}\n")
    
    # Test 5: Update user
    print("5. Testing Update User (PUT /users/1)")
    update_data = {
        "name": "John Doe Updated",
        "email": "<EMAIL>"
    }
    response = requests.put(f"{BASE_URL}/users/1", json=update_data)
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}\n")
    
    # Test 6: Get all users again to see changes
    print("6. Testing Get All Users Again (GET /users)")
    response = requests.get(f"{BASE_URL}/users")
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}\n")
    
    # Test 7: Delete user
    print("7. Testing Delete User (DELETE /users/2)")
    response = requests.delete(f"{BASE_URL}/users/2")
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}\n")
    
    # Test 8: Try to get deleted user (should return 404)
    print("8. Testing Get Deleted User (GET /users/2)")
    response = requests.get(f"{BASE_URL}/users/2")
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}\n")
    
    # Test 9: Error handling - Invalid endpoint
    print("9. Testing Invalid Endpoint (GET /invalid)")
    response = requests.get(f"{BASE_URL}/invalid")
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}\n")

def interactive_test():
    """Interactive testing mode"""
    print("🔧 Interactive API Testing Mode")
    print("Available commands:")
    print("1. GET /users - Get all users")
    print("2. GET /users/<id> - Get user by ID")
    print("3. POST /users - Create new user")
    print("4. PUT /users/<id> - Update user")
    print("5. DELETE /users/<id> - Delete user")
    print("6. quit - Exit")
    
    while True:
        command = input("\nEnter command (1-6): ").strip()
        
        if command == "1":
            response = requests.get(f"{BASE_URL}/users")
            print(f"Status: {response.status_code}")
            print(f"Response: {json.dumps(response.json(), indent=2)}")
            
        elif command == "2":
            user_id = input("Enter user ID: ")
            response = requests.get(f"{BASE_URL}/users/{user_id}")
            print(f"Status: {response.status_code}")
            print(f"Response: {json.dumps(response.json(), indent=2)}")
            
        elif command == "3":
            name = input("Enter name: ")
            email = input("Enter email: ")
            new_user = {"name": name, "email": email}
            response = requests.post(f"{BASE_URL}/users", json=new_user)
            print(f"Status: {response.status_code}")
            print(f"Response: {json.dumps(response.json(), indent=2)}")
            
        elif command == "4":
            user_id = input("Enter user ID to update: ")
            name = input("Enter new name (or press Enter to skip): ")
            email = input("Enter new email (or press Enter to skip): ")
            update_data = {}
            if name: update_data["name"] = name
            if email: update_data["email"] = email
            response = requests.put(f"{BASE_URL}/users/{user_id}", json=update_data)
            print(f"Status: {response.status_code}")
            print(f"Response: {json.dumps(response.json(), indent=2)}")
            
        elif command == "5":
            user_id = input("Enter user ID to delete: ")
            response = requests.delete(f"{BASE_URL}/users/{user_id}")
            print(f"Status: {response.status_code}")
            print(f"Response: {json.dumps(response.json(), indent=2)}")
            
        elif command == "6" or command.lower() == "quit":
            print("Goodbye!")
            break
            
        else:
            print("Invalid command. Please enter 1-6.")

if __name__ == "__main__":
    print("Choose testing mode:")
    print("1. Automated test")
    print("2. Interactive test")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "1":
        test_api()
    elif choice == "2":
        interactive_test()
    else:
        print("Invalid choice. Running automated test...")
        test_api()
