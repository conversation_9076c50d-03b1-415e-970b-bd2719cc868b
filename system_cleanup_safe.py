#!/usr/bin/env python3
"""
System Cleanup Script - Safe Mode (Dry Run)
Shows what files would be deleted without actually deleting them.
Safer version for testing before running the actual cleanup.
"""

import os
import sys
import ctypes
import getpass
from pathlib import Path
from datetime import datetime

def is_admin():
    """Check if the script is running with administrator privileges"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def get_folder_info(folder_path):
    """Get information about files in a folder without deleting them"""
    if not os.path.exists(folder_path):
        return 0, 0, 0, []
    
    file_count = 0
    folder_count = 0
    total_size = 0
    sample_files = []
    
    try:
        for root, dirs, files in os.walk(folder_path):
            folder_count += len(dirs)
            for file in files:
                file_count += 1
                file_path = os.path.join(root, file)
                try:
                    file_size = os.path.getsize(file_path)
                    total_size += file_size
                    
                    # Collect sample files (first 10)
                    if len(sample_files) < 10:
                        modified_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                        sample_files.append({
                            'name': file,
                            'size': file_size,
                            'path': file_path,
                            'modified': modified_time
                        })
                except (OSError, FileNotFoundError):
                    continue
    except (OSError, PermissionError):
        pass
    
    return file_count, folder_count, total_size, sample_files

def format_size(size_bytes):
    """Convert bytes to human readable format"""
    if size_bytes == 0:
        return "0 B"
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    return f"{size_bytes:.2f} {size_names[i]}"

def analyze_folder(folder_path, folder_name):
    """Analyze folder contents without deleting"""
    print(f"\n📁 Analyzing {folder_name}...")
    print(f"   Path: {folder_path}")
    
    if not os.path.exists(folder_path):
        print(f"   ⚠️  Folder does not exist")
        return
    
    try:
        file_count, folder_count, total_size, sample_files = get_folder_info(folder_path)
        
        print(f"   📊 Files found: {file_count}")
        print(f"   📊 Folders found: {folder_count}")
        print(f"   📊 Total size: {format_size(total_size)}")
        
        if sample_files:
            print(f"   📄 Sample files (showing first {min(len(sample_files), 10)}):")
            for file_info in sample_files:
                print(f"      • {file_info['name']} ({format_size(file_info['size'])}) - {file_info['modified'].strftime('%Y-%m-%d %H:%M')}")
        
        if file_count == 0 and folder_count == 0:
            print(f"   ✅ Folder is already empty")
        
    except PermissionError:
        print(f"   ❌ Permission denied - Administrator privileges required")
    except Exception as e:
        print(f"   ❌ Error analyzing folder: {e}")

def main():
    """Main function to analyze system folders"""
    print("🔍 Windows System Cleanup - Analysis Mode")
    print("=" * 55)
    print("This script will ANALYZE (not delete) temporary files in:")
    
    # Get current username for user temp folder
    username = getpass.getuser()
    
    # Define folders to analyze
    folders_to_analyze = [
        (r"C:\Windows\Prefetch", "Windows Prefetch"),
        (r"C:\Windows\Temp", "Windows Temp"),
        (rf"C:\Users\<USER>\AppData\Local\Temp", "User Temp")
    ]
    
    print(f"\n📂 Target folders:")
    for folder_path, folder_name in folders_to_analyze:
        exists = "✅" if os.path.exists(folder_path) else "❌"
        print(f"   {exists} {folder_name}: {folder_path}")
    
    if not is_admin():
        print(f"\n⚠️  Note: Running without Administrator privileges")
        print("   Some folders may not be accessible")
    else:
        print(f"\n✅ Running with Administrator privileges")
    
    print(f"\n🔍 Starting analysis...")
    
    total_files = 0
    total_folders = 0
    total_size = 0
    
    for folder_path, folder_name in folders_to_analyze:
        analyze_folder(folder_path, folder_name)
        
        # Add to totals
        if os.path.exists(folder_path):
            try:
                file_count, folder_count, size, _ = get_folder_info(folder_path)
                total_files += file_count
                total_folders += folder_count
                total_size += size
            except:
                pass
    
    # Summary
    print(f"\n" + "=" * 55)
    print(f"📊 ANALYSIS SUMMARY")
    print(f"   Total files that would be deleted: {total_files}")
    print(f"   Total folders that would be deleted: {total_folders}")
    print(f"   Total space that would be freed: {format_size(total_size)}")
    
    if total_files > 0 or total_folders > 0:
        print(f"\n💡 To actually perform the cleanup:")
        print(f"   Run 'system_cleanup.py' (the main cleanup script)")
        print(f"   Or double-click 'run_cleanup.bat'")
    else:
        print(f"\n✅ No files to clean - your system is already clean!")
    
    print(f"\n⚠️  IMPORTANT NOTES:")
    print(f"   • This was a DRY RUN - no files were deleted")
    print(f"   • Some files may be in use and cannot be deleted")
    print(f"   • Administrator privileges are required for actual cleanup")
    
    input(f"\nPress Enter to exit...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ Operation cancelled by user.")
        input("Press Enter to exit...")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        input("Press Enter to exit...")
