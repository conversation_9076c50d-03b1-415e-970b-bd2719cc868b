@echo off
title Image Analyzer - Object & Person Detection
echo.
echo ========================================
echo    Image Analyzer Tool
echo    Object & Person Detection
echo ========================================
echo.

echo Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

echo Python found. Checking dependencies...

REM Install required packages
echo Installing required packages...
pip install google-generativeai Pillow --quiet

if %errorlevel% neq 0 (
    echo WARNING: Some packages may not have installed correctly
    echo You can manually install with: pip install -r image_analyzer_requirements.txt
)

echo.
echo Choose mode:
echo 1. GUI Mode (Graphical Interface)
echo 2. CLI Mode (Command Line)
echo 3. Interactive CLI Mode
echo.

set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" (
    echo Starting GUI mode...
    python image_analyzer.py
) else if "%choice%"=="2" (
    set /p imagepath="Enter image path: "
    echo Starting CLI analysis...
    python image_analyzer_cli.py "%imagepath%"
) else if "%choice%"=="3" (
    echo Starting interactive CLI mode...
    python image_analyzer_cli.py --interactive
) else (
    echo Invalid choice. Starting GUI mode by default...
    python image_analyzer.py
)

echo.
echo Analysis completed.
pause
