#!/usr/bin/env python3
"""
Test script for Password Manager & URL Organizer
Verifies all functionality works correctly
"""

import os
import sys
import json
import base64
from pathlib import Path

def test_imports():
    """Test if all required modules can be imported"""
    print("🧪 Testing Imports...")
    
    modules_to_test = [
        'tkinter',
        'json', 
        'base64',
        'webbrowser',
        'os',
        'pathlib',
        'subprocess'
    ]
    
    failed_imports = []
    
    for module in modules_to_test:
        try:
            __import__(module)
            print(f"  ✅ {module}")
        except ImportError as e:
            print(f"  ❌ {module}: {e}")
            failed_imports.append(module)
    
    return len(failed_imports) == 0, failed_imports

def test_file_operations():
    """Test file read/write operations"""
    print("\n📁 Testing File Operations...")
    
    desktop_path = Path.home() / "Desktop"
    test_file = desktop_path / "test_password_manager.json"
    
    # Test data
    test_data = {
        "test": "data",
        "number": 123,
        "list": [1, 2, 3]
    }
    
    try:
        # Test write
        with open(test_file, 'w') as f:
            json.dump(test_data, f)
        print("  ✅ File write successful")
        
        # Test read
        with open(test_file, 'r') as f:
            loaded_data = json.load(f)
        
        if loaded_data == test_data:
            print("  ✅ File read successful")
        else:
            print("  ❌ File read failed - data mismatch")
            return False
        
        # Clean up
        os.remove(test_file)
        print("  ✅ File cleanup successful")
        
        return True
        
    except Exception as e:
        print(f"  ❌ File operations failed: {e}")
        return False

def test_encoding():
    """Test password encoding/decoding"""
    print("\n🔐 Testing Password Encoding...")
    
    test_passwords = [
        "SimplePassword",
        "Complex!Password@123",
        "Unicode测试密码",
        "Special#$%^&*()Characters"
    ]
    
    try:
        for password in test_passwords:
            # Encode
            encoded = base64.b64encode(password.encode()).decode()
            
            # Decode
            decoded = base64.b64decode(encoded.encode()).decode()
            
            if decoded == password:
                print(f"  ✅ '{password[:10]}...' encoding successful")
            else:
                print(f"  ❌ '{password[:10]}...' encoding failed")
                return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ Encoding test failed: {e}")
        return False

def test_gui_components():
    """Test GUI components"""
    print("\n🖥️  Testing GUI Components...")
    
    try:
        import tkinter as tk
        from tkinter import ttk
        
        # Create test window
        root = tk.Tk()
        root.withdraw()  # Hide window
        
        # Test basic widgets
        frame = ttk.Frame(root)
        label = ttk.Label(frame, text="Test")
        entry = ttk.Entry(frame)
        button = ttk.Button(frame, text="Test")
        text = tk.Text(frame, height=1, width=10)
        
        print("  ✅ Basic widgets created successfully")
        
        # Test notebook (tabs)
        notebook = ttk.Notebook(frame)
        tab1 = ttk.Frame(notebook)
        notebook.add(tab1, text="Tab 1")
        
        print("  ✅ Notebook (tabs) created successfully")
        
        # Test treeview
        tree = ttk.Treeview(frame, columns=("col1", "col2"), show="headings")
        tree.heading("col1", text="Column 1")
        tree.heading("col2", text="Column 2")
        
        print("  ✅ Treeview created successfully")
        
        # Cleanup
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"  ❌ GUI test failed: {e}")
        return False

def test_webbrowser():
    """Test webbrowser module"""
    print("\n🌐 Testing Web Browser Integration...")
    
    try:
        import webbrowser
        
        # Just test if we can get a browser
        browser = webbrowser.get()
        print(f"  ✅ Default browser available: {browser.name if hasattr(browser, 'name') else 'Unknown'}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Browser test failed: {e}")
        return False

def test_main_application():
    """Test if main application can be imported"""
    print("\n🚀 Testing Main Application...")
    
    try:
        # Check if main file exists
        if not os.path.exists("password_manager.py"):
            print("  ❌ password_manager.py not found")
            return False
        
        print("  ✅ Main application file found")
        
        # Try to import (but don't run)
        import password_manager
        print("  ✅ Main application imports successfully")
        
        # Check if main class exists
        if hasattr(password_manager, 'PasswordManager'):
            print("  ✅ PasswordManager class found")
        else:
            print("  ❌ PasswordManager class not found")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ Main application test failed: {e}")
        return False

def create_sample_data():
    """Create sample data for testing"""
    print("\n📝 Creating Sample Data...")
    
    desktop_path = Path.home() / "Desktop"
    
    # Sample data
    sample_passwords = [
        {
            "title": "Test Email",
            "username": "<EMAIL>",
            "password": base64.b64encode("TestPassword123".encode()).decode(),
            "notes": "Test password entry"
        }
    ]
    
    sample_urls = [
        {
            "title": "Test Website",
            "url": "https://www.example.com",
            "category": "Test",
            "description": "Test URL entry"
        }
    ]
    
    sample_settings = {
        "background_color": "#f0f0f0",
        "theme": "default"
    }
    
    try:
        # Create sample files
        with open(desktop_path / "passwords_data.json", 'w') as f:
            json.dump(sample_passwords, f, indent=2)
        print("  ✅ Sample passwords created")
        
        with open(desktop_path / "urls_data.json", 'w') as f:
            json.dump(sample_urls, f, indent=2)
        print("  ✅ Sample URLs created")
        
        with open(desktop_path / "app_settings.json", 'w') as f:
            json.dump(sample_settings, f, indent=2)
        print("  ✅ Sample settings created")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Sample data creation failed: {e}")
        return False

def run_all_tests():
    """Run all tests"""
    print("🧪 PASSWORD MANAGER TEST SUITE")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("File Operations Test", test_file_operations),
        ("Password Encoding Test", test_encoding),
        ("GUI Components Test", test_gui_components),
        ("Web Browser Test", test_webbrowser),
        ("Main Application Test", test_main_application)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    # Summary
    print(f"\n{'='*50}")
    print(f"📊 TEST SUMMARY")
    print(f"{'='*50}")
    print(f"Passed: {passed_tests}/{total_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED! Application is ready to use.")
        
        # Offer to create sample data
        create_sample = input("\nCreate sample data for testing? (y/n): ").lower().startswith('y')
        if create_sample:
            create_sample_data()
        
        # Offer to run application
        run_app = input("\nRun the Password Manager now? (y/n): ").lower().startswith('y')
        if run_app:
            try:
                print("\n🚀 Starting Password Manager...")
                import password_manager
                app = password_manager.PasswordManager()
                app.run()
            except Exception as e:
                print(f"❌ Failed to start application: {e}")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        print("💡 Make sure Python 3.6+ is installed with tkinter support.")

def main():
    """Main test function"""
    print("Choose test mode:")
    print("1. Run all tests")
    print("2. Create sample data only")
    print("3. Test specific component")
    
    choice = input("\nEnter choice (1-3): ").strip()
    
    if choice == "1":
        run_all_tests()
    elif choice == "2":
        create_sample_data()
    elif choice == "3":
        print("\nSpecific tests:")
        print("a. Import test")
        print("b. File operations")
        print("c. Password encoding")
        print("d. GUI components")
        print("e. Web browser")
        print("f. Main application")
        
        test_choice = input("Enter test (a-f): ").lower()
        
        test_map = {
            'a': test_imports,
            'b': test_file_operations,
            'c': test_encoding,
            'd': test_gui_components,
            'e': test_webbrowser,
            'f': test_main_application
        }
        
        if test_choice in test_map:
            test_map[test_choice]()
        else:
            print("Invalid choice")
    else:
        print("Invalid choice")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
