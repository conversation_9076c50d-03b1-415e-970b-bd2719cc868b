@echo off
title Password Manager & URL Organizer
echo.
echo ========================================
echo    Password Manager & URL Organizer
echo ========================================
echo.
echo Starting application...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo.
    echo Please install Python from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

REM Run the password manager
python password_manager.py

REM Check if there was an error
if %errorlevel% neq 0 (
    echo.
    echo ERROR: Application failed to start
    echo Please check the error messages above
    echo.
    pause
)

echo.
echo Application closed.
pause
