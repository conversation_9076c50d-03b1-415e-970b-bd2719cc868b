# FLUX Authentication Troubleshooting Guide

## 🔧 Common Authentication Issues & Solutions

### Issue 1: "Unable to authenticate" Error

**Possible Causes:**
- Outdated `use_auth_token` parameter (now deprecated)
- Invalid or expired token
- Network connectivity issues
- Insufficient permissions for the model

**Solutions:**

#### ✅ Solution 1: Updated Authentication (FIXED)
The script now uses the correct `token` parameter instead of `use_auth_token`:
```python
pipeline = DiffusionPipeline.from_pretrained(
    "black-forest-labs/FLUX.1-dev",
    token="your_token_here"  # ✅ Correct
    # use_auth_token="token"  # ❌ Deprecated
)
```

#### ✅ Solution 2: Login First (Recommended)
```python
from huggingface_hub import login
login(token="your_token_here")
# Then load the pipeline
```

#### ✅ Solution 3: Environment Variable (Most Secure)
```bash
# Set environment variable
set HUGGINGFACE_TOKEN=your_token_here
# Then run the script
```

### Issue 2: Token Validation

**Check if your token is valid:**
1. Go to [Hugging Face Settings](https://huggingface.co/settings/tokens)
2. Verify your token exists and has the right permissions
3. Make sure it has "Read" access to repositories

**Token Format:**
- Should start with `hf_`
- Should be exactly 37 characters long
- Example: `*************************************`

### Issue 3: Model Access Permissions

**FLUX.1-dev requires:**
- Hugging Face account
- Acceptance of model license
- Valid authentication token

**Steps to get access:**
1. Visit: https://huggingface.co/black-forest-labs/FLUX.1-dev
2. Click "Agree and access repository"
3. Accept the license terms
4. Use your token in the script

### Issue 4: Network/Firewall Issues

**Symptoms:**
- Connection timeouts
- SSL certificate errors
- Network unreachable errors

**Solutions:**
- Check internet connection
- Try using a VPN if behind corporate firewall
- Disable antivirus temporarily to test
- Use `--trust-remote-code` flag if needed

## 🚀 Quick Fixes to Try

### Fix 1: Update Dependencies
```bash
pip install --upgrade diffusers transformers huggingface_hub torch
```

### Fix 2: Clear Hugging Face Cache
```bash
# Windows
rmdir /s "%USERPROFILE%\.cache\huggingface"

# Or in Python
from huggingface_hub import scan_cache_dir
scan_cache_dir().delete_revisions()
```

### Fix 3: Manual Login
```python
from huggingface_hub import login
login()  # This will prompt for token interactively
```

### Fix 4: Alternative Loading Method
```python
# Try FluxPipeline instead of DiffusionPipeline
from diffusers import FluxPipeline
pipeline = FluxPipeline.from_pretrained(
    "black-forest-labs/FLUX.1-dev",
    token="your_token"
)
```

## 📋 Debugging Steps

### Step 1: Test Token Validity
```python
from huggingface_hub import whoami
try:
    user_info = whoami(token="your_token")
    print(f"Token is valid for user: {user_info['name']}")
except Exception as e:
    print(f"Token validation failed: {e}")
```

### Step 2: Test Model Access
```python
from huggingface_hub import model_info
try:
    info = model_info("black-forest-labs/FLUX.1-dev", token="your_token")
    print("Model access: OK")
except Exception as e:
    print(f"Model access failed: {e}")
```

### Step 3: Check Dependencies
```python
import diffusers
import transformers
import torch
print(f"diffusers: {diffusers.__version__}")
print(f"transformers: {transformers.__version__}")
print(f"torch: {torch.__version__}")
```

## 🔄 Alternative Approaches

### Approach 1: Use Local Model (if downloaded)
```python
pipeline = DiffusionPipeline.from_pretrained(
    "./local_flux_model",  # Local path
    torch_dtype=torch.bfloat16
)
```

### Approach 2: Use Different Model
```python
# Try FLUX.1-schnell (may have different requirements)
pipeline = DiffusionPipeline.from_pretrained(
    "black-forest-labs/FLUX.1-schnell",
    token="your_token"
)
```

### Approach 3: Use Hugging Face Inference API
```python
import requests

def query_flux_api(prompt):
    API_URL = "https://api-inference.huggingface.co/models/black-forest-labs/FLUX.1-dev"
    headers = {"Authorization": f"Bearer your_token"}
    
    response = requests.post(API_URL, headers=headers, json={"inputs": prompt})
    return response.content
```

## 📞 Getting Help

If none of these solutions work:

1. **Check Hugging Face Status**: https://status.huggingface.co/
2. **Hugging Face Forums**: https://discuss.huggingface.co/
3. **GitHub Issues**: https://github.com/huggingface/diffusers/issues
4. **Discord**: Hugging Face Discord server

## 🔍 Error Code Reference

| Error | Meaning | Solution |
|-------|---------|----------|
| 401 | Unauthorized | Check token validity |
| 403 | Forbidden | Accept model license |
| 404 | Not Found | Check model name |
| 429 | Rate Limited | Wait and retry |
| 500 | Server Error | Try again later |

---

**Last Updated**: Current version includes all the latest fixes for authentication issues.
