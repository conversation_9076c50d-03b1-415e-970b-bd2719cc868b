#!/usr/bin/env python3
"""
Test script for Image Analyzer with visual annotations
Creates a sample image and tests the analysis functionality
"""

import os
import sys
from PIL import Image, ImageDraw, ImageFont
import random

def create_test_image():
    """Create a simple test image with objects and text"""
    # Create a 800x600 image with white background
    img = Image.new('RGB', (800, 600), color='white')
    draw = ImageDraw.Draw(img)
    
    # Draw some simple objects
    # Rectangle (representing a building)
    draw.rectangle([50, 100, 200, 300], fill='lightblue', outline='blue', width=3)
    draw.text((60, 110), "BUILDING", fill='black')
    
    # Circle (representing a person)
    draw.ellipse([300, 200, 380, 280], fill='pink', outline='red', width=3)
    draw.text((310, 230), "PERSON", fill='black')
    
    # Car shape
    draw.rectangle([500, 350, 650, 420], fill='red', outline='darkred', width=3)
    draw.ellipse([510, 410, 540, 440], fill='black')  # Wheel
    draw.ellipse([620, 410, 650, 440], fill='black')  # Wheel
    draw.text((520, 370), "CAR", fill='white')
    
    # Face
    draw.ellipse([400, 50, 500, 150], fill='peach', outline='brown', width=2)
    draw.ellipse([420, 80, 430, 90], fill='black')  # Eye
    draw.ellipse([470, 80, 480, 90], fill='black')  # Eye
    draw.arc([430, 100, 470, 130], 0, 180, fill='red', width=2)  # Smile
    
    # Add some text tags
    draw.text((50, 50), "Test Image for Analysis", fill='black', 
              font=None)
    draw.text((50, 550), "Tags: building, person, car, face, outdoor", 
              fill='gray')
    
    # Save the test image
    test_image_path = "test_image.jpg"
    img.save(test_image_path)
    print(f"✅ Created test image: {test_image_path}")
    return test_image_path

def test_cli_mode():
    """Test CLI mode with image display"""
    print("\n🧪 Testing CLI Mode...")
    test_image = create_test_image()
    
    # Import the analyzer
    try:
        from image_analyzer_standalone import ImageAnalyzer, CLIInterface
        
        analyzer = ImageAnalyzer()
        cli = CLIInterface()
        
        print("🔍 Analyzing test image...")
        results = analyzer.analyze_image(test_image, detailed=True)
        
        print("📊 Displaying results...")
        cli.display_results(results, "text", image_path=test_image, show_image=True)
        
        print("✅ CLI test completed!")
        
    except Exception as e:
        print(f"❌ CLI test failed: {e}")

def test_gui_mode():
    """Test GUI mode"""
    print("\n🖥️  Testing GUI Mode...")
    try:
        from image_analyzer_standalone import GUIInterface, GUI_AVAILABLE
        
        if not GUI_AVAILABLE:
            print("⚠️  GUI not available - skipping GUI test")
            return
        
        print("🚀 Starting GUI (close the window when done testing)...")
        gui = GUIInterface()
        
        # Show instructions
        print("\n📋 GUI Test Instructions:")
        print("1. Click 'Upload Image' and select the test_image.jpg")
        print("2. Check 'Detailed Analysis' and 'Show Annotated Image'")
        print("3. Click 'Analyze Image'")
        print("4. View the results and annotated image")
        print("5. Close the GUI window when done")
        
        gui.run()
        print("✅ GUI test completed!")
        
    except Exception as e:
        print(f"❌ GUI test failed: {e}")

def test_annotation_features():
    """Test the annotation features specifically"""
    print("\n🎨 Testing Annotation Features...")
    
    try:
        from image_analyzer_standalone import ImageAnalyzer
        
        test_image = create_test_image()
        analyzer = ImageAnalyzer()
        
        # Create mock results for testing annotation
        mock_results = {
            "objects": [
                {
                    "name": "building",
                    "description": "Blue rectangular building",
                    "bbox": {"x": 0.0625, "y": 0.167, "width": 0.1875, "height": 0.333}
                },
                {
                    "name": "car",
                    "description": "Red car with wheels",
                    "bbox": {"x": 0.625, "y": 0.583, "width": 0.1875, "height": 0.117}
                }
            ],
            "persons": [
                {
                    "count": "1",
                    "description": "Pink circular person representation",
                    "bbox": {"x": 0.375, "y": 0.333, "width": 0.1, "height": 0.133}
                }
            ],
            "faces": [
                {
                    "description": "Smiling face",
                    "expression": "happy",
                    "bbox": {"x": 0.5, "y": 0.083, "width": 0.125, "height": 0.167}
                }
            ],
            "tags": ["building", "person", "car", "face", "test", "colorful", "simple"],
            "summary": "Test image with various objects and a face"
        }
        
        print("🖼️  Creating annotated image...")
        annotated_image = analyzer.create_annotated_image(test_image, mock_results, 
                                                        "test_image_annotated.jpg")
        
        if annotated_image:
            print("✅ Annotated image created successfully!")
            print("📁 Check 'test_image_annotated.jpg' to see the result")
            
            # Try to display it
            try:
                analyzer.display_image_with_matplotlib(test_image, mock_results)
            except Exception as e:
                print(f"⚠️  Could not display image: {e}")
        else:
            print("❌ Failed to create annotated image")
            
    except Exception as e:
        print(f"❌ Annotation test failed: {e}")

def main():
    """Main test function"""
    print("🧪 IMAGE ANALYZER TEST SUITE")
    print("=" * 50)
    
    # Check if we can import the main module
    try:
        import image_analyzer_standalone
        print("✅ Successfully imported image_analyzer_standalone")
    except ImportError as e:
        print(f"❌ Failed to import main module: {e}")
        print("💡 Make sure image_analyzer_standalone.py is in the same directory")
        return
    
    print("\nChoose test mode:")
    print("1. Test CLI Mode (with image display)")
    print("2. Test GUI Mode")
    print("3. Test Annotation Features")
    print("4. Run All Tests")
    
    choice = input("\nEnter choice (1-4): ").strip()
    
    if choice == "1":
        test_cli_mode()
    elif choice == "2":
        test_gui_mode()
    elif choice == "3":
        test_annotation_features()
    elif choice == "4":
        test_annotation_features()
        test_cli_mode()
        test_gui_mode()
    else:
        print("Invalid choice. Running annotation test...")
        test_annotation_features()
    
    print("\n🎉 Testing completed!")
    print("📁 Check the generated files:")
    print("   • test_image.jpg - Original test image")
    print("   • test_image_annotated.jpg - Annotated version")

if __name__ == "__main__":
    main()
