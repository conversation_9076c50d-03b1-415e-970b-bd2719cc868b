from flask import Flask, request, jsonify
from datetime import datetime
import json

# Initialize Flask app
app = Flask(__name__)

# Sample data - In real applications, this would be a database
users = [
    {"id": 1, "name": "<PERSON>", "email": "<EMAIL>", "created_at": "2024-01-01"},
    {"id": 2, "name": "<PERSON>", "email": "<EMAIL>", "created_at": "2024-01-02"}
]

# Helper function to find user by ID
def find_user(user_id):
    return next((user for user in users if user["id"] == user_id), None)

# Helper function to get next ID
def get_next_id():
    return max([user["id"] for user in users], default=0) + 1

# Routes (API Endpoints)

@app.route('/')
def home():
    """Welcome endpoint"""
    return jsonify({
        "message": "Welcome to the REST API!",
        "endpoints": {
            "GET /users": "Get all users",
            "GET /users/<id>": "Get user by ID",
            "POST /users": "Create new user",
            "PUT /users/<id>": "Update user by ID",
            "DELETE /users/<id>": "Delete user by ID"
        }
    })

# GET - Retrieve all users
@app.route('/users', methods=['GET'])
def get_users():
    """Get all users"""
    return jsonify({
        "success": True,
        "data": users,
        "count": len(users)
    })

# GET - Retrieve a specific user by ID
@app.route('/users/<int:user_id>', methods=['GET'])
def get_user(user_id):
    """Get user by ID"""
    user = find_user(user_id)
    if user:
        return jsonify({
            "success": True,
            "data": user
        })
    else:
        return jsonify({
            "success": False,
            "message": "User not found"
        }), 404

# POST - Create a new user
@app.route('/users', methods=['POST'])
def create_user():
    """Create a new user"""
    try:
        data = request.get_json()
        
        # Validate required fields
        if not data or 'name' not in data or 'email' not in data:
            return jsonify({
                "success": False,
                "message": "Name and email are required"
            }), 400
        
        # Check if email already exists
        if any(user['email'] == data['email'] for user in users):
            return jsonify({
                "success": False,
                "message": "Email already exists"
            }), 400
        
        # Create new user
        new_user = {
            "id": get_next_id(),
            "name": data['name'],
            "email": data['email'],
            "created_at": datetime.now().strftime("%Y-%m-%d")
        }
        
        users.append(new_user)
        
        return jsonify({
            "success": True,
            "message": "User created successfully",
            "data": new_user
        }), 201
        
    except Exception as e:
        return jsonify({
            "success": False,
            "message": "Error creating user",
            "error": str(e)
        }), 500

# PUT - Update an existing user
@app.route('/users/<int:user_id>', methods=['PUT'])
def update_user(user_id):
    """Update user by ID"""
    try:
        user = find_user(user_id)
        if not user:
            return jsonify({
                "success": False,
                "message": "User not found"
            }), 404
        
        data = request.get_json()
        if not data:
            return jsonify({
                "success": False,
                "message": "No data provided"
            }), 400
        
        # Update user fields
        if 'name' in data:
            user['name'] = data['name']
        if 'email' in data:
            # Check if email already exists for other users
            if any(u['email'] == data['email'] and u['id'] != user_id for u in users):
                return jsonify({
                    "success": False,
                    "message": "Email already exists"
                }), 400
            user['email'] = data['email']
        
        return jsonify({
            "success": True,
            "message": "User updated successfully",
            "data": user
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "message": "Error updating user",
            "error": str(e)
        }), 500

# DELETE - Delete a user
@app.route('/users/<int:user_id>', methods=['DELETE'])
def delete_user(user_id):
    """Delete user by ID"""
    user = find_user(user_id)
    if not user:
        return jsonify({
            "success": False,
            "message": "User not found"
        }), 404
    
    users.remove(user)
    return jsonify({
        "success": True,
        "message": "User deleted successfully"
    })

# Error handlers
@app.errorhandler(404)
def not_found(error):
    return jsonify({
        "success": False,
        "message": "Endpoint not found"
    }), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        "success": False,
        "message": "Internal server error"
    }), 500

if __name__ == '__main__':
    print("Starting Flask REST API...")
    print("Available endpoints:")
    print("- GET    /users       - Get all users")
    print("- GET    /users/<id>  - Get user by ID")
    print("- POST   /users       - Create new user")
    print("- PUT    /users/<id>  - Update user by ID")
    print("- DELETE /users/<id>  - Delete user by ID")
    print("\nServer running on http://localhost:5000")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
