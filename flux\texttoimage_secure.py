import torch
import os
from diffusers import FluxPipeline
from diffusers import DiffusionPipeline
from huggingface_hub import login

def get_huggingface_token():
    """Get Hugging Face token from environment variable or user input"""
    # Try to get token from environment variable first
    token = os.getenv('HUGGINGFACE_TOKEN')
    
    if not token:
        # If not found in environment, use the hardcoded token as fallback
        token = "*************************************"
        print("⚠️  Using hardcoded token. For better security, set HUGGINGFACE_TOKEN environment variable.")
    else:
        print("✅ Using token from environment variable")
    
    return token

def authenticate_huggingface():
    """Authenticate with Hugging Face Hub"""
    token = get_huggingface_token()
    
    if not token:
        print("❌ No Hugging Face token found!")
        print("Please either:")
        print("1. Set HUGGINGFACE_TOKEN environment variable")
        print("2. Update the hardcoded token in the script")
        return False
    
    try:
        # Method 1: Login using huggingface_hub
        login(token=token)
        print("✅ Successfully authenticated with Hugging Face Hub")
        return token
    except Exception as e:
        print(f"❌ Authentication failed: {e}")
        print("Please check your token and try again")
        return False

def load_flux_pipeline(token):
    """Load the FLUX pipeline with proper authentication"""
    try:
        print("📥 Loading FLUX.1-dev pipeline...")
        
        # Try different loading methods
        pipeline = DiffusionPipeline.from_pretrained(
            "black-forest-labs/FLUX.1-dev",
            token=token,
            torch_dtype=torch.bfloat16,
            device_map="auto"
        )
        
        print("✅ Pipeline loaded successfully")
        return pipeline
        
    except Exception as e:
        print(f"❌ Failed to load pipeline: {e}")
        
        # Try alternative loading method
        try:
            print("🔄 Trying alternative loading method...")
            pipeline = FluxPipeline.from_pretrained(
                "black-forest-labs/FLUX.1-dev",
                token=token,
                torch_dtype=torch.bfloat16
            )
            print("✅ Pipeline loaded with FluxPipeline")
            return pipeline
        except Exception as e2:
            print(f"❌ Alternative method also failed: {e2}")
            return None

def generate_image(pipeline, prompt, output_filename="flux-generated.png"):
    """Generate image using the pipeline"""
    try:
        print(f"🎨 Generating image with prompt: '{prompt}'")
        print("⏳ This may take a few minutes...")
        
        image = pipeline(
            prompt,
            height=1024,
            width=1024,
            guidance_scale=3.5,
            num_inference_steps=50,
            max_sequence_length=512,
            generator=torch.Generator("cpu").manual_seed(0)
        ).images[0]
        
        # Save the image
        image.save(output_filename)
        print(f"✅ Image saved successfully as '{output_filename}'")
        return True
        
    except Exception as e:
        print(f"❌ Failed to generate image: {e}")
        return False

def main():
    """Main function"""
    print("🚀 FLUX.1-dev Text-to-Image Generator")
    print("=" * 50)
    
    # Step 1: Authenticate
    token = authenticate_huggingface()
    if not token:
        exit(1)
    
    # Step 2: Load pipeline
    pipeline = load_flux_pipeline(token)
    if not pipeline:
        exit(1)
    
    # Step 3: Generate image
    prompt = "A cat holding a sign that says hello world"
    success = generate_image(pipeline, prompt)
    
    if success:
        print("\n🎉 Image generation completed successfully!")
    else:
        print("\n❌ Image generation failed!")
        exit(1)

if __name__ == "__main__":
    main()
