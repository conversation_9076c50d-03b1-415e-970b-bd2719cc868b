# Windows System Cleanup Scripts

This collection of scripts helps you clean temporary files from Windows system folders to free up disk space and improve system performance.

## 📁 Files Included

1. **`system_cleanup.py`** - Main cleanup script (DELETES files)
2. **`system_cleanup_safe.py`** - Analysis script (SAFE - shows what would be deleted)
3. **`run_cleanup.bat`** - Batch file to run the main script easily
4. **`CLEANUP_README.md`** - This documentation

## 🎯 What These Scripts Do

The scripts target these Windows temporary file locations:
- **`C:\Windows\Prefetch`** - Windows prefetch files
- **`C:\Windows\Temp`** - Windows system temporary files  
- **`C:\Users\<USER>\AppData\Local\Temp`** - User temporary files

## 🚀 How to Use

### Option 1: Safe Analysis First (Recommended)
1. **Run the safe script first** to see what would be deleted:
   ```
   python system_cleanup_safe.py
   ```
   This shows you exactly what files would be deleted without actually deleting them.

2. **If you're happy with the analysis**, run the actual cleanup:
   ```
   python system_cleanup.py
   ```

### Option 2: Direct Cleanup
1. **Double-click** `run_cleanup.bat`
   OR
2. **Run from command prompt**:
   ```
   python system_cleanup.py
   ```

### Option 3: Right-click "Run as Administrator"
- Right-click on `system_cleanup.py` → "Run as Administrator"

## ⚠️ Important Safety Information

### Before Running:
- **Close all programs** to avoid "file in use" errors
- **Create a system restore point** (optional but recommended)
- **Run the safe analysis script first** to see what will be deleted

### Administrator Privileges Required:
- These scripts **MUST** run as Administrator
- Windows will prompt for elevation when needed
- Some system folders cannot be accessed without admin rights

### What Gets Deleted:
- ✅ Temporary files and folders
- ✅ Cache files
- ✅ Prefetch files
- ❌ **Does NOT delete** important system files
- ❌ **Does NOT delete** your personal files

## 🛡️ Safety Features

### Built-in Protections:
- **Confirmation prompt** before deleting anything
- **Administrator privilege check**
- **Folder existence verification**
- **Error handling** for files in use
- **Detailed progress reporting**
- **Size calculation** showing space freed

### Error Handling:
- Files in use are skipped (not deleted)
- Permission errors are reported but don't stop the script
- Missing folders are handled gracefully

## 📊 What You'll See

### During Analysis (Safe Mode):
```
🔍 Windows System Cleanup - Analysis Mode
===============================================

📂 Target folders:
   ✅ Windows Prefetch: C:\Windows\Prefetch
   ✅ Windows Temp: C:\Windows\Temp
   ✅ User Temp: C:\Users\<USER>\AppData\Local\Temp

📁 Analyzing Windows Prefetch...
   📊 Files found: 1,234
   📊 Total size: 45.67 MB
   📄 Sample files:
      • CHROME.EXE-A1B2C3D4.pf (128 KB)
      • NOTEPAD.EXE-E5F6G7H8.pf (64 KB)
```

### During Actual Cleanup:
```
🧹 Cleaning Windows Temp...
   📊 Initial size: 234.56 MB
   ✅ Files deleted: 1,456
   ✅ Folders deleted: 23
   💾 Space freed: 234.56 MB
```

## 🔧 Troubleshooting

### "Permission Denied" Error:
- **Solution**: Run as Administrator
- Right-click script → "Run as Administrator"

### "Python not found" Error:
- **Solution**: Install Python from [python.org](https://python.org)
- Make sure "Add to PATH" is checked during installation

### "Some files could not be deleted":
- **Normal behavior** - files in use by Windows cannot be deleted
- Try closing all programs and running again
- Restart computer and run script immediately after boot

### Script Won't Start:
- Check if Python is installed: `python --version`
- Try running from Command Prompt as Administrator

## 💡 Tips for Best Results

### When to Run:
- **After closing all programs**
- **During system maintenance**
- **When disk space is low**
- **Monthly or as needed**

### What to Expect:
- **Typical space freed**: 100MB - 2GB depending on usage
- **Time to complete**: 30 seconds - 2 minutes
- **Some files will be skipped** (this is normal)

## 🔒 Security Notes

### These Scripts Are Safe Because:
- **Only target temporary folders** (not system files)
- **Include confirmation prompts**
- **Handle errors gracefully**
- **Don't modify registry or system settings**

### What They DON'T Do:
- ❌ Delete personal documents
- ❌ Uninstall programs
- ❌ Modify system settings
- ❌ Delete important Windows files

## 📈 Benefits of Regular Cleanup

- **Free up disk space**
- **Improve system performance**
- **Reduce clutter**
- **Help with system maintenance**

## 🆘 Need Help?

If you encounter issues:
1. **Run the safe analysis script first**
2. **Check if you're running as Administrator**
3. **Close all programs before running**
4. **Restart computer if files are locked**

---

**⚠️ DISCLAIMER**: While these scripts are designed to be safe, always ensure you have backups of important data. Use at your own risk.
