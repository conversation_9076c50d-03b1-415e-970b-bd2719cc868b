#!/usr/bin/env python3
"""
Setup Script for Image Analyzer
Ensures all dependencies are installed and the tool is ready to use.
"""

import os
import sys
import subprocess
import platform

def print_header():
    """Print setup header"""
    print("=" * 60)
    print("🔧 IMAGE ANALYZER SETUP")
    print("=" * 60)
    print("This script will install all required dependencies")
    print("for the Image Analyzer tool.\n")

def check_python_version():
    """Check if Python version is compatible"""
    print("🐍 Checking Python version...")
    version = sys.version_info
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print(f"❌ Python {version.major}.{version.minor} detected")
        print("⚠️  Python 3.7 or higher is required")
        print("💡 Please upgrade Python from https://python.org")
        return False
    else:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
        return True

def check_pip():
    """Check if pip is available"""
    print("\n📦 Checking pip...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "--version"], 
                            stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        print("✅ pip is available")
        return True
    except subprocess.CalledProcessError:
        print("❌ pip is not available")
        print("💡 Please install pip or use a Python distribution that includes it")
        return False

def install_dependencies():
    """Install required dependencies"""
    print("\n📚 Installing dependencies...")
    
    dependencies = [
        ("google-generativeai", "Google Gemini API client"),
        ("Pillow", "Image processing library"),
    ]
    
    failed_packages = []
    
    for package, description in dependencies:
        print(f"\n📦 Installing {package} ({description})...")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package, "--upgrade"
            ])
            print(f"✅ {package} installed successfully")
        except subprocess.CalledProcessError:
            print(f"❌ Failed to install {package}")
            failed_packages.append(package)
    
    return failed_packages

def check_tkinter():
    """Check if tkinter is available for GUI"""
    print("\n🖥️  Checking GUI support (tkinter)...")
    try:
        import tkinter
        print("✅ tkinter is available - GUI mode will work")
        return True
    except ImportError:
        print("⚠️  tkinter not found - only CLI mode will be available")
        print("💡 On Ubuntu/Debian: sudo apt-get install python3-tk")
        print("💡 On CentOS/RHEL: sudo yum install tkinter")
        print("💡 On Windows/Mac: tkinter should be included with Python")
        return False

def test_imports():
    """Test if all required modules can be imported"""
    print("\n🧪 Testing imports...")
    
    modules = [
        ("google.generativeai", "Google Gemini API"),
        ("PIL", "Pillow (Image processing)"),
    ]
    
    failed_imports = []
    
    for module, description in modules:
        try:
            __import__(module)
            print(f"✅ {description} - OK")
        except ImportError as e:
            print(f"❌ {description} - Failed: {e}")
            failed_imports.append(module)
    
    return failed_imports

def create_test_script():
    """Create a simple test script"""
    test_script = """#!/usr/bin/env python3
# Quick test for Image Analyzer dependencies

try:
    import google.generativeai as genai
    print("✅ Google Generative AI - OK")
except ImportError as e:
    print(f"❌ Google Generative AI - Failed: {e}")

try:
    from PIL import Image
    print("✅ Pillow (PIL) - OK")
except ImportError as e:
    print(f"❌ Pillow (PIL) - Failed: {e}")

try:
    import tkinter
    print("✅ tkinter (GUI) - OK")
except ImportError as e:
    print("⚠️  tkinter (GUI) - Not available (CLI only)")

print("\\n🎉 Dependency check complete!")
"""
    
    with open("test_dependencies.py", "w") as f:
        f.write(test_script)
    
    print("📝 Created test_dependencies.py")
    print("💡 Run 'python test_dependencies.py' anytime to check dependencies")

def show_usage_instructions():
    """Show how to use the Image Analyzer"""
    print("\n" + "=" * 60)
    print("🎉 SETUP COMPLETE!")
    print("=" * 60)
    print("\n📖 HOW TO USE:")
    print("\n1. GUI Mode (Recommended):")
    print("   python image_analyzer.py")
    print("   python image_analyzer_standalone.py")
    
    print("\n2. Command Line Mode:")
    print("   python image_analyzer_cli.py image.jpg")
    print("   python image_analyzer_standalone.py image.jpg")
    
    print("\n3. Interactive Mode:")
    print("   python image_analyzer_cli.py --interactive")
    print("   python image_analyzer_standalone.py --interactive")
    
    print("\n4. Detailed Analysis:")
    print("   python image_analyzer_standalone.py image.jpg --detailed")
    
    print("\n📁 SUPPORTED FORMATS:")
    print("   JPEG, PNG, BMP, GIF, TIFF")
    
    print("\n🔑 API KEY:")
    print("   The Gemini API key is already configured in the scripts")
    print("   For production use, consider using environment variables")
    
    print("\n💡 TIPS:")
    print("   • Use the standalone version for easy sharing")
    print("   • GUI mode is most user-friendly")
    print("   • CLI mode is better for batch processing")
    
    print("\n🆘 NEED HELP?")
    print("   • Check IMAGE_ANALYZER_README.md for detailed documentation")
    print("   • Run with --help for command line options")
    print("   • Test dependencies with: python test_dependencies.py")

def main():
    """Main setup function"""
    print_header()
    
    # Check system requirements
    if not check_python_version():
        sys.exit(1)
    
    if not check_pip():
        sys.exit(1)
    
    # Install dependencies
    failed_packages = install_dependencies()
    
    # Check GUI support
    gui_available = check_tkinter()
    
    # Test imports
    failed_imports = test_imports()
    
    # Create test script
    create_test_script()
    
    # Show results
    if failed_packages or failed_imports:
        print("\n⚠️  SETUP COMPLETED WITH WARNINGS")
        if failed_packages:
            print(f"❌ Failed to install: {', '.join(failed_packages)}")
        if failed_imports:
            print(f"❌ Import failures: {', '.join(failed_imports)}")
        print("\n💡 You may need to install these manually:")
        for package in failed_packages:
            print(f"   pip install {package}")
    else:
        print("\n✅ ALL DEPENDENCIES INSTALLED SUCCESSFULLY!")
    
    if not gui_available:
        print("\n⚠️  GUI mode not available - CLI mode only")
    
    show_usage_instructions()

if __name__ == "__main__":
    main()
