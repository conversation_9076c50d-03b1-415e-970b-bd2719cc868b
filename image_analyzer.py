#!/usr/bin/env python3
"""
Image Analysis Tool using Google Gemini API
Detects objects, persons, and provides tags for uploaded images.
Auto-installs required dependencies.
"""

import os
import sys
import json
import subprocess
from pathlib import Path

def install_package(package_name, import_name=None):
    """Install a package if it's not already installed"""
    if import_name is None:
        import_name = package_name

    try:
        __import__(import_name)
        return True
    except ImportError:
        print(f"📦 Installing {package_name}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
            print(f"✅ Successfully installed {package_name}")
            return True
        except subprocess.CalledProcessError:
            print(f"❌ Failed to install {package_name}")
            return False

def check_and_install_dependencies():
    """Check and install all required dependencies"""
    print("🔍 Checking dependencies...")

    dependencies = [
        ("google-generativeai", "google.generativeai"),
        ("Pillow", "PIL"),
    ]

    all_installed = True
    for package_name, import_name in dependencies:
        if not install_package(package_name, import_name):
            all_installed = False

    # Check tkinter (usually comes with Python)
    try:
        import tkinter
        print("✅ tkinter is available")
    except ImportError:
        print("❌ tkinter is not available. Please install Python with tkinter support.")
        all_installed = False

    if all_installed:
        print("✅ All dependencies are ready!")
    else:
        print("❌ Some dependencies failed to install. Please install them manually.")
        sys.exit(1)

# Install dependencies before importing them
check_and_install_dependencies()

# Now import the required modules
import google.generativeai as genai
from PIL import Image
import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext
from tkinter import ttk

class ImageAnalyzer:
    def __init__(self):
        self.api_key = "AIzaSyBcL4rVwXdB1wzNVOMndNPzCm27SYJo_Co"
        self.setup_gemini()
        
    def setup_gemini(self):
        """Initialize Gemini API"""
        try:
            genai.configure(api_key=self.api_key)
            self.model = genai.GenerativeModel('gemini-1.5-flash')
            print("✅ Gemini API initialized successfully")
        except Exception as e:
            print(f"❌ Failed to initialize Gemini API: {e}")
            sys.exit(1)
    
    def analyze_image(self, image_path):
        """Analyze image using Gemini API"""
        try:
            # Open and prepare image
            image = Image.open(image_path)
            
            # Create detailed prompt for object detection
            prompt = """
            Analyze this image and provide a detailed analysis in JSON format with the following structure:
            {
                "objects": [
                    {
                        "name": "object_name",
                        "confidence": "high/medium/low",
                        "description": "brief description",
                        "location": "approximate location in image"
                    }
                ],
                "persons": [
                    {
                        "count": "number of people",
                        "description": "description of people (age group, gender if clear, activity)",
                        "location": "where they are in the image"
                    }
                ],
                "scene": {
                    "setting": "indoor/outdoor/etc",
                    "environment": "description of the environment",
                    "lighting": "lighting conditions",
                    "mood": "overall mood/atmosphere"
                },
                "tags": [
                    "relevant", "descriptive", "tags", "for", "the", "image"
                ],
                "summary": "A brief overall description of the image"
            }
            
            Be thorough and accurate. Focus on clearly visible objects and people.
            """
            
            # Generate content
            response = self.model.generate_content([prompt, image])
            
            return self.parse_response(response.text)
            
        except Exception as e:
            return {"error": f"Analysis failed: {str(e)}"}
    
    def parse_response(self, response_text):
        """Parse Gemini response and extract JSON"""
        try:
            # Try to find JSON in the response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response_text[start_idx:end_idx]
                return json.loads(json_str)
            else:
                # If no JSON found, create structured response from text
                return {
                    "summary": response_text,
                    "objects": [],
                    "persons": [],
                    "scene": {"setting": "unknown"},
                    "tags": [],
                    "raw_response": response_text
                }
        except json.JSONDecodeError:
            return {
                "summary": response_text,
                "objects": [],
                "persons": [],
                "scene": {"setting": "unknown"},
                "tags": [],
                "raw_response": response_text,
                "error": "Could not parse JSON response"
            }

class ImageAnalyzerGUI:
    def __init__(self):
        self.analyzer = ImageAnalyzer()
        self.setup_gui()
        
    def setup_gui(self):
        """Setup the GUI interface"""
        self.root = tk.Tk()
        self.root.title("Image Analyzer - Object & Person Detection")
        self.root.geometry("800x600")
        
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="🔍 Image Analysis Tool", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Upload button
        self.upload_btn = ttk.Button(main_frame, text="📁 Upload Image", 
                                    command=self.upload_image)
        self.upload_btn.grid(row=1, column=0, sticky=tk.W, pady=(0, 10))
        
        # Selected file label
        self.file_label = ttk.Label(main_frame, text="No file selected")
        self.file_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=(0, 10))
        
        # Analyze button
        self.analyze_btn = ttk.Button(main_frame, text="🔍 Analyze Image", 
                                     command=self.analyze_image, state="disabled")
        self.analyze_btn.grid(row=2, column=0, sticky=tk.W, pady=(0, 20))
        
        # Progress bar
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Results area
        results_label = ttk.Label(main_frame, text="Analysis Results:", 
                                 font=("Arial", 12, "bold"))
        results_label.grid(row=4, column=0, sticky=tk.W, pady=(10, 5))
        
        # Text area for results
        self.results_text = scrolledtext.ScrolledText(main_frame, height=25, width=80)
        self.results_text.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), 
                              pady=(0, 10))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(5, weight=1)
        
        self.selected_file = None
    
    def upload_image(self):
        """Handle image upload"""
        file_types = [
            ("Image files", "*.jpg *.jpeg *.png *.bmp *.gif *.tiff"),
            ("JPEG files", "*.jpg *.jpeg"),
            ("PNG files", "*.png"),
            ("All files", "*.*")
        ]
        
        filename = filedialog.askopenfilename(
            title="Select an image file",
            filetypes=file_types
        )
        
        if filename:
            self.selected_file = filename
            self.file_label.config(text=f"Selected: {Path(filename).name}")
            self.analyze_btn.config(state="normal")
            self.results_text.delete(1.0, tk.END)
    
    def analyze_image(self):
        """Analyze the selected image"""
        if not self.selected_file:
            messagebox.showerror("Error", "Please select an image first")
            return
        
        # Start progress bar
        self.progress.start()
        self.analyze_btn.config(state="disabled")
        self.root.update()
        
        try:
            # Analyze image
            results = self.analyzer.analyze_image(self.selected_file)
            
            # Display results
            self.display_results(results)
            
        except Exception as e:
            messagebox.showerror("Error", f"Analysis failed: {str(e)}")
        finally:
            # Stop progress bar
            self.progress.stop()
            self.analyze_btn.config(state="normal")
    
    def display_results(self, results):
        """Display analysis results in the text area"""
        self.results_text.delete(1.0, tk.END)
        
        if "error" in results:
            self.results_text.insert(tk.END, f"❌ Error: {results['error']}\n\n")
            if "raw_response" in results:
                self.results_text.insert(tk.END, f"Raw Response:\n{results['raw_response']}")
            return
        
        # Format and display results
        output = []
        output.append("🎯 IMAGE ANALYSIS RESULTS")
        output.append("=" * 50)
        
        # Summary
        if "summary" in results and results["summary"]:
            output.append(f"\n📋 SUMMARY:")
            output.append(f"{results['summary']}")
        
        # Objects detected
        if "objects" in results and results["objects"]:
            output.append(f"\n🎯 OBJECTS DETECTED ({len(results['objects'])}):")
            for i, obj in enumerate(results["objects"], 1):
                output.append(f"  {i}. {obj.get('name', 'Unknown')}")
                if 'confidence' in obj:
                    output.append(f"     Confidence: {obj['confidence']}")
                if 'description' in obj:
                    output.append(f"     Description: {obj['description']}")
                if 'location' in obj:
                    output.append(f"     Location: {obj['location']}")
                output.append("")
        
        # Persons detected
        if "persons" in results and results["persons"]:
            output.append(f"\n👥 PERSONS DETECTED:")
            for person in results["persons"]:
                if 'count' in person:
                    output.append(f"  Count: {person['count']}")
                if 'description' in person:
                    output.append(f"  Description: {person['description']}")
                if 'location' in person:
                    output.append(f"  Location: {person['location']}")
                output.append("")
        
        # Scene information
        if "scene" in results and results["scene"]:
            output.append(f"\n🏞️ SCENE INFORMATION:")
            scene = results["scene"]
            if 'setting' in scene:
                output.append(f"  Setting: {scene['setting']}")
            if 'environment' in scene:
                output.append(f"  Environment: {scene['environment']}")
            if 'lighting' in scene:
                output.append(f"  Lighting: {scene['lighting']}")
            if 'mood' in scene:
                output.append(f"  Mood: {scene['mood']}")
            output.append("")
        
        # Tags
        if "tags" in results and results["tags"]:
            output.append(f"\n🏷️ TAGS ({len(results['tags'])}):")
            tags_str = ", ".join(results["tags"])
            output.append(f"  {tags_str}")
        
        # Display in text widget
        self.results_text.insert(tk.END, "\n".join(output))
    
    def run(self):
        """Start the GUI application"""
        self.root.mainloop()

def main():
    """Main function"""
    print("🚀 Starting Image Analyzer...")
    
    # Check if running in GUI mode or command line mode
    if len(sys.argv) > 1:
        # Command line mode
        image_path = sys.argv[1]
        if not os.path.exists(image_path):
            print(f"❌ Image file not found: {image_path}")
            sys.exit(1)
        
        analyzer = ImageAnalyzer()
        print(f"🔍 Analyzing image: {image_path}")
        results = analyzer.analyze_image(image_path)
        
        # Print results
        print("\n" + "="*50)
        print("ANALYSIS RESULTS")
        print("="*50)
        print(json.dumps(results, indent=2))
    else:
        # GUI mode
        app = ImageAnalyzerGUI()
        app.run()

if __name__ == "__main__":
    main()
