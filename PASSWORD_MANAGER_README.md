# 🔐 Password Manager & URL Organizer

A standalone desktop application for managing passwords and organizing URLs with no external dependencies required.

## 🎯 Features

### Password Management
- **Save Passwords**: Store username/password combinations with titles and notes
- **Quick Copy**: One-click copy username or password to clipboard
- **Show/Hide Password**: Toggle password visibility while typing
- **Edit & Delete**: Modify or remove saved passwords
- **Search & Organize**: Easy-to-browse password list

### URL Management
- **Save URLs**: Store website links with titles, categories, and descriptions
- **Quick Open**: One-click to open URLs in default browser
- **Copy URLs**: Copy URL to clipboard
- **Categorize**: Organize URLs by category
- **Edit & Delete**: Modify or remove saved URLs

### Customization
- **Background Colors**: Choose custom background colors
- **Themes**: Customizable appearance
- **Data Management**: Backup and import functionality

### Security & Storage
- **Desktop Storage**: All data saved to JSON files on desktop
- **Basic Encoding**: Passwords are base64 encoded for basic protection
- **No Cloud**: All data stays on your local machine
- **Standalone**: No external dependencies or installations required

## 📁 Files Created

The application creates these files on your desktop:
- **`passwords_data.json`** - Stores all password data
- **`urls_data.json`** - Stores all URL bookmarks
- **`app_settings.json`** - Stores application settings

## 🚀 How to Use

### Quick Start
1. **Double-click** `run_password_manager.bat` (Windows)
2. **Or run directly**: `python password_manager.py`

### Password Management
1. **Add Password**:
   - Go to "🔐 Passwords" tab
   - Fill in Title, Username, Password, and Notes
   - Click "💾 Save Password"

2. **Copy Credentials**:
   - Select a password from the list
   - Click "📋 Copy Username" or "🔑 Copy Password"
   - Or double-click entry to copy username

3. **Edit Password**:
   - Select password and click "✏️ Edit"
   - Modify fields and save again

### URL Management
1. **Add URL**:
   - Go to "🔗 URLs" tab
   - Fill in Title, URL, Category, and Description
   - Click "💾 Save URL"

2. **Open URL**:
   - Select URL from list
   - Click "🌐 Open URL" or double-click entry
   - URL opens in default browser

3. **Copy URL**:
   - Select URL and click "📋 Copy URL"

### Customization
1. **Change Background**:
   - Go to "⚙️ Settings" tab
   - Click "Choose Color" under Background Color
   - Select your preferred color

2. **Data Management**:
   - **Backup**: Save data files to another location
   - **Import**: Restore data from backup
   - **Open Folder**: View data files on desktop

## 🔒 Security Notes

### What's Protected
- Passwords are **base64 encoded** for basic protection
- Data is stored **locally only** (no cloud sync)
- No network connections except for opening URLs

### Security Limitations
- **Not encrypted** - suitable for team/office use, not highly sensitive data
- **Visible to anyone** with access to your computer
- **No master password** protection

### For Enhanced Security
- Use for **non-critical passwords** only
- Consider a **dedicated password manager** for sensitive accounts
- Keep your **computer secure** with user account protection

## 💻 System Requirements

### Minimum Requirements
- **Python 3.6+** (Download from [python.org](https://python.org))
- **Windows, macOS, or Linux**
- **No additional libraries** required (uses built-in Python modules)

### What's Included
- **tkinter** - GUI framework (built into Python)
- **json** - Data storage (built into Python)
- **base64** - Password encoding (built into Python)
- **webbrowser** - URL opening (built into Python)
- **os, sys, pathlib** - System operations (built into Python)

## 📋 Usage Examples

### Example Password Entry
```
Title: Gmail Account
Username: <EMAIL>
Password: MySecurePassword123
Notes: Personal email account, 2FA enabled
```

### Example URL Entry
```
Title: Company Dashboard
URL: https://dashboard.company.com
Category: Work
Description: Main company dashboard for daily tasks
```

## 🛠️ Troubleshooting

### "Python not found"
- **Install Python** from [python.org](https://python.org)
- **Check "Add to PATH"** during installation
- **Restart command prompt** after installation

### "Application won't start"
- **Run from command line**: `python password_manager.py`
- **Check error messages** in console
- **Ensure Python 3.6+** is installed

### "Can't save data"
- **Check desktop permissions** (write access)
- **Close other applications** that might lock files
- **Run as administrator** if needed

### "GUI looks wrong"
- **Update Python** to latest version
- **Check tkinter installation**: `python -m tkinter`
- **Try different theme** in settings

## 📊 Data Format

### Password Data Structure
```json
{
  "title": "Website Name",
  "username": "<EMAIL>", 
  "password": "base64_encoded_password",
  "notes": "Additional notes"
}
```

### URL Data Structure
```json
{
  "title": "Website Title",
  "url": "https://example.com",
  "category": "Work",
  "description": "Website description"
}
```

## 🔄 Backup & Sharing

### Manual Backup
1. Copy these files from desktop:
   - `passwords_data.json`
   - `urls_data.json`
   - `app_settings.json`

### Team Sharing
1. **Share the application**: Send `password_manager.py` and `run_password_manager.bat`
2. **Share data** (optional): Send JSON files to team members
3. **Import data**: Use Settings → Import Data

### Migration
1. **Export**: Use Settings → Backup Data
2. **Transfer**: Copy backup files to new computer
3. **Import**: Use Settings → Import Data

## 🎨 Customization Tips

### Background Colors
- **Light themes**: #f0f0f0, #ffffff, #f5f5f5
- **Dark themes**: #2d2d2d, #1e1e1e, #333333
- **Colored themes**: #e6f3ff, #fff0e6, #f0fff0

### Organization Tips
- **Use categories** for URLs (Work, Personal, Tools, etc.)
- **Add detailed notes** for passwords (security questions, etc.)
- **Use descriptive titles** for easy searching
- **Regular backups** to prevent data loss

## 🆘 Support

### Getting Help
1. **Check this README** for common solutions
2. **Run from command line** to see error messages
3. **Check Python installation**: `python --version`
4. **Verify file permissions** on desktop

### Common Issues
- **Slow startup**: Normal for first run
- **Missing buttons**: Window too small - resize it
- **Copy not working**: Select an item first
- **URL won't open**: Check URL format (needs http/https)

---

**🎉 Enjoy using your Password Manager & URL Organizer!**

*This tool is designed for team productivity and convenience. For highly sensitive data, consider using a dedicated security solution.*
