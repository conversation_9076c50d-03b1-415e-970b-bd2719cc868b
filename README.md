# Python REST API Tutorial

This project demonstrates how to create a REST API in Python using Flask framework.

## What is a REST API?

REST (Representational State Transfer) is an architectural style for designing web services. It uses standard HTTP methods and follows these principles:

- **Stateless**: Each request contains all information needed
- **Resource-based**: Everything is treated as a resource with unique URLs
- **HTTP methods**: Uses GET, POST, PUT, DELETE
- **JSON format**: Data exchange in JSON format

## Project Structure

```
├── app.py              # Main Flask application
├── test_api.py         # API testing client
├── requirements.txt    # Python dependencies
└── README.md          # This documentation
```

## Installation & Setup

### 1. Install Python Dependencies

```bash
pip install -r requirements.txt
```

### 2. Run the API Server

```bash
python app.py
```

The server will start on `http://localhost:5000`

### 3. Test the API

In a new terminal, run:

```bash
python test_api.py
```

## API Endpoints

### Base URL: `http://localhost:5000`

| Method | Endpoint | Description | Request Body |
|--------|----------|-------------|--------------|
| GET | `/` | Welcome message | None |
| GET | `/users` | Get all users | None |
| GET | `/users/<id>` | Get user by ID | None |
| POST | `/users` | Create new user | `{"name": "string", "email": "string"}` |
| PUT | `/users/<id>` | Update user | `{"name": "string", "email": "string"}` |
| DELETE | `/users/<id>` | Delete user | None |

## API Usage Examples

### 1. Get All Users
```bash
curl -X GET http://localhost:5000/users
```

### 2. Get User by ID
```bash
curl -X GET http://localhost:5000/users/1
```

### 3. Create New User
```bash
curl -X POST http://localhost:5000/users \
  -H "Content-Type: application/json" \
  -d '{"name": "Alice Johnson", "email": "<EMAIL>"}'
```

### 4. Update User
```bash
curl -X PUT http://localhost:5000/users/1 \
  -H "Content-Type: application/json" \
  -d '{"name": "John Updated", "email": "<EMAIL>"}'
```

### 5. Delete User
```bash
curl -X DELETE http://localhost:5000/users/1
```

## Response Format

All responses follow this format:

### Success Response
```json
{
  "success": true,
  "data": {...},
  "message": "Optional message"
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error description",
  "error": "Optional error details"
}
```

## HTTP Status Codes

- `200 OK` - Successful GET, PUT, DELETE
- `201 Created` - Successful POST
- `400 Bad Request` - Invalid request data
- `404 Not Found` - Resource not found
- `500 Internal Server Error` - Server error

## Key Concepts Explained

### 1. Flask Framework
Flask is a lightweight Python web framework that makes it easy to build web applications and APIs.

### 2. HTTP Methods
- **GET**: Retrieve data
- **POST**: Create new data
- **PUT**: Update existing data
- **DELETE**: Remove data

### 3. JSON Data Format
All data is exchanged in JSON format, which is lightweight and easy to parse.

### 4. Error Handling
The API includes proper error handling with appropriate HTTP status codes and error messages.

### 5. Data Validation
Input data is validated before processing to ensure data integrity.

## Next Steps

To enhance this API, you could:

1. **Add Database**: Replace in-memory storage with a real database (SQLite, PostgreSQL, etc.)
2. **Add Authentication**: Implement JWT tokens or API keys
3. **Add Pagination**: For large datasets
4. **Add Filtering**: Query parameters for filtering results
5. **Add Logging**: Track API usage and errors
6. **Add Rate Limiting**: Prevent API abuse
7. **Add Documentation**: Use Swagger/OpenAPI for interactive docs

## Common Issues & Solutions

### Issue: "Connection refused"
**Solution**: Make sure the Flask server is running (`python app.py`)

### Issue: "Module not found"
**Solution**: Install dependencies (`pip install -r requirements.txt`)

### Issue: "Port already in use"
**Solution**: Change the port in `app.py` or kill the process using the port

## Learning Resources

- [Flask Documentation](https://flask.palletsprojects.com/)
- [REST API Best Practices](https://restfulapi.net/)
- [HTTP Status Codes](https://httpstatuses.com/)
- [JSON Format](https://www.json.org/)

Happy coding! 🚀
