#!/usr/bin/env python3
"""
Standalone Password Manager & URL Organizer
Saves passwords and URLs to JSON files on desktop
No external dependencies required - uses only built-in Python libraries
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, colorchooser
import json
import os
import webbrowser
import base64
from pathlib import Path
import subprocess
import sys

class PasswordManager:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔐 Password Manager & URL Organizer")
        self.root.geometry("800x600")
        self.root.resizable(True, True)

        # Get desktop path
        self.desktop_path = self.get_desktop_path()
        self.passwords_file = os.path.join(self.desktop_path, "passwords_data.json")
        self.urls_file = os.path.join(self.desktop_path, "urls_data.json")
        self.settings_file = os.path.join(self.desktop_path, "app_settings.json")

        # Data storage
        self.passwords_data = []
        self.urls_data = []
        self.settings = {"background_color": "#f0f0f0", "theme": "default"}

        # Load data
        self.load_data()
        self.setup_gui()
        self.apply_settings()

    def get_desktop_path(self):
        """Get desktop path for different operating systems"""
        if os.name == 'nt':  # Windows
            return os.path.join(os.path.expanduser('~'), 'Desktop')
        else:  # macOS and Linux
            return os.path.join(os.path.expanduser('~'), 'Desktop')

    def setup_gui(self):
        """Setup the main GUI"""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create tabs
        self.setup_passwords_tab()
        self.setup_urls_tab()
        self.setup_settings_tab()

        # Status bar
        self.status_bar = tk.Label(self.root, text="Ready", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def setup_passwords_tab(self):
        """Setup passwords management tab"""
        # Passwords tab
        self.passwords_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.passwords_frame, text="🔐 Passwords")

        # Input frame
        input_frame = ttk.LabelFrame(self.passwords_frame, text="Add New Password", padding=10)
        input_frame.pack(fill=tk.X, padx=10, pady=5)

        # Title
        ttk.Label(input_frame, text="Title:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.title_entry = ttk.Entry(input_frame, width=30)
        self.title_entry.grid(row=0, column=1, padx=5, pady=2, sticky=tk.W+tk.E)

        # Username
        ttk.Label(input_frame, text="Username:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.username_entry = ttk.Entry(input_frame, width=30)
        self.username_entry.grid(row=1, column=1, padx=5, pady=2, sticky=tk.W+tk.E)

        # Password
        ttk.Label(input_frame, text="Password:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.password_entry = ttk.Entry(input_frame, width=30, show="*")
        self.password_entry.grid(row=2, column=1, padx=5, pady=2, sticky=tk.W+tk.E)

        # Show/Hide password
        self.show_password_var = tk.BooleanVar()
        show_password_check = ttk.Checkbutton(input_frame, text="Show Password",
                                            variable=self.show_password_var,
                                            command=self.toggle_password_visibility)
        show_password_check.grid(row=2, column=2, padx=5, pady=2)

        # Notes
        ttk.Label(input_frame, text="Notes:").grid(row=3, column=0, sticky=tk.W+tk.N, pady=2)
        self.notes_text = tk.Text(input_frame, width=30, height=3)
        self.notes_text.grid(row=3, column=1, padx=5, pady=2, sticky=tk.W+tk.E)

        # Buttons frame
        buttons_frame = ttk.Frame(input_frame)
        buttons_frame.grid(row=4, column=0, columnspan=3, pady=10)

        ttk.Button(buttons_frame, text="💾 Save Password",
                  command=self.save_password).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="🔄 Clear Fields",
                  command=self.clear_password_fields).pack(side=tk.LEFT, padx=5)

        # Configure grid weights
        input_frame.columnconfigure(1, weight=1)

        # Passwords list frame
        list_frame = ttk.LabelFrame(self.passwords_frame, text="Saved Passwords", padding=10)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Treeview for passwords
        columns = ("Title", "Username", "Notes")
        self.passwords_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)

        # Configure columns
        self.passwords_tree.heading("Title", text="Title")
        self.passwords_tree.heading("Username", text="Username")
        self.passwords_tree.heading("Notes", text="Notes")

        self.passwords_tree.column("Title", width=200)
        self.passwords_tree.column("Username", width=200)
        self.passwords_tree.column("Notes", width=300)

        # Scrollbar for treeview
        passwords_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL,
                                          command=self.passwords_tree.yview)
        self.passwords_tree.configure(yscrollcommand=passwords_scrollbar.set)

        # Pack treeview and scrollbar
        self.passwords_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        passwords_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Action buttons frame
        action_frame = ttk.Frame(self.passwords_frame)
        action_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(action_frame, text="📋 Copy Username",
                  command=self.copy_username).pack(side=tk.LEFT, padx=5)
        ttk.Button(action_frame, text="🔑 Copy Password",
                  command=self.copy_password).pack(side=tk.LEFT, padx=5)
        ttk.Button(action_frame, text="✏️ Edit",
                  command=self.edit_password).pack(side=tk.LEFT, padx=5)
        ttk.Button(action_frame, text="🗑️ Delete",
                  command=self.delete_password).pack(side=tk.LEFT, padx=5)

        # Bind double-click to copy username
        self.passwords_tree.bind("<Double-1>", lambda e: self.copy_username())

    def setup_urls_tab(self):
        """Setup URLs management tab"""
        # URLs tab
        self.urls_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.urls_frame, text="🔗 URLs")

        # Input frame
        url_input_frame = ttk.LabelFrame(self.urls_frame, text="Add New URL", padding=10)
        url_input_frame.pack(fill=tk.X, padx=10, pady=5)

        # Title
        ttk.Label(url_input_frame, text="Title:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.url_title_entry = ttk.Entry(url_input_frame, width=40)
        self.url_title_entry.grid(row=0, column=1, padx=5, pady=2, sticky=tk.W+tk.E)

        # URL
        ttk.Label(url_input_frame, text="URL:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.url_entry = ttk.Entry(url_input_frame, width=40)
        self.url_entry.grid(row=1, column=1, padx=5, pady=2, sticky=tk.W+tk.E)

        # Category
        ttk.Label(url_input_frame, text="Category:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.url_category_entry = ttk.Entry(url_input_frame, width=40)
        self.url_category_entry.grid(row=2, column=1, padx=5, pady=2, sticky=tk.W+tk.E)

        # Description
        ttk.Label(url_input_frame, text="Description:").grid(row=3, column=0, sticky=tk.W+tk.N, pady=2)
        self.url_description_text = tk.Text(url_input_frame, width=40, height=3)
        self.url_description_text.grid(row=3, column=1, padx=5, pady=2, sticky=tk.W+tk.E)

        # URL Buttons frame
        url_buttons_frame = ttk.Frame(url_input_frame)
        url_buttons_frame.grid(row=4, column=0, columnspan=2, pady=10)

        ttk.Button(url_buttons_frame, text="💾 Save URL",
                  command=self.save_url).pack(side=tk.LEFT, padx=5)
        ttk.Button(url_buttons_frame, text="🔄 Clear Fields",
                  command=self.clear_url_fields).pack(side=tk.LEFT, padx=5)

        # Configure grid weights
        url_input_frame.columnconfigure(1, weight=1)

        # URLs list frame
        url_list_frame = ttk.LabelFrame(self.urls_frame, text="Saved URLs", padding=10)
        url_list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Treeview for URLs
        url_columns = ("Title", "URL", "Category", "Description")
        self.urls_tree = ttk.Treeview(url_list_frame, columns=url_columns, show="headings", height=15)

        # Configure columns
        self.urls_tree.heading("Title", text="Title")
        self.urls_tree.heading("URL", text="URL")
        self.urls_tree.heading("Category", text="Category")
        self.urls_tree.heading("Description", text="Description")

        self.urls_tree.column("Title", width=150)
        self.urls_tree.column("URL", width=250)
        self.urls_tree.column("Category", width=100)
        self.urls_tree.column("Description", width=200)

        # Scrollbar for URLs treeview
        urls_scrollbar = ttk.Scrollbar(url_list_frame, orient=tk.VERTICAL,
                                     command=self.urls_tree.yview)
        self.urls_tree.configure(yscrollcommand=urls_scrollbar.set)

        # Pack URLs treeview and scrollbar
        self.urls_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        urls_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # URL Action buttons frame
        url_action_frame = ttk.Frame(self.urls_frame)
        url_action_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(url_action_frame, text="🌐 Open URL",
                  command=self.open_url).pack(side=tk.LEFT, padx=5)
        ttk.Button(url_action_frame, text="📋 Copy URL",
                  command=self.copy_url).pack(side=tk.LEFT, padx=5)
        ttk.Button(url_action_frame, text="✏️ Edit",
                  command=self.edit_url).pack(side=tk.LEFT, padx=5)
        ttk.Button(url_action_frame, text="🗑️ Delete",
                  command=self.delete_url).pack(side=tk.LEFT, padx=5)

        # Bind double-click to open URL
        self.urls_tree.bind("<Double-1>", lambda e: self.open_url())

    def setup_settings_tab(self):
        """Setup settings tab"""
        # Settings tab
        self.settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.settings_frame, text="⚙️ Settings")

        # Appearance frame
        appearance_frame = ttk.LabelFrame(self.settings_frame, text="Appearance", padding=10)
        appearance_frame.pack(fill=tk.X, padx=10, pady=10)

        # Background color
        ttk.Label(appearance_frame, text="Background Color:").grid(row=0, column=0, sticky=tk.W, pady=5)

        color_frame = ttk.Frame(appearance_frame)
        color_frame.grid(row=0, column=1, sticky=tk.W, padx=10)

        self.color_preview = tk.Label(color_frame, text="  Preview  ",
                                    bg=self.settings["background_color"],
                                    relief=tk.RAISED, width=10)
        self.color_preview.pack(side=tk.LEFT, padx=5)

        ttk.Button(color_frame, text="Choose Color",
                  command=self.choose_background_color).pack(side=tk.LEFT, padx=5)
        ttk.Button(color_frame, text="Reset Default",
                  command=self.reset_background_color).pack(side=tk.LEFT, padx=5)

        # Data management frame
        data_frame = ttk.LabelFrame(self.settings_frame, text="Data Management", padding=10)
        data_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(data_frame, text="📁 Open Data Folder",
                  command=self.open_data_folder).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(data_frame, text="💾 Backup Data",
                  command=self.backup_data).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(data_frame, text="📥 Import Data",
                  command=self.import_data).pack(side=tk.LEFT, padx=5, pady=5)

        # Info frame
        info_frame = ttk.LabelFrame(self.settings_frame, text="Information", padding=10)
        info_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        info_text = f"""
🔐 Password Manager & URL Organizer
Version: 1.0
Author: Team Tool

📁 Data Location: {self.desktop_path}
📄 Files:
  • passwords_data.json - Password storage
  • urls_data.json - URL storage
  • app_settings.json - Application settings

🔒 Security Note:
Passwords are encoded (not encrypted) for basic protection.
For sensitive data, consider using a dedicated password manager.

💡 Features:
  • Save and organize passwords
  • Quick copy username/password
  • URL bookmark management
  • Customizable appearance
  • Standalone - no dependencies
        """

        info_label = tk.Label(info_frame, text=info_text, justify=tk.LEFT,
                            font=("Consolas", 9), bg="white", relief=tk.SUNKEN)
        info_label.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def toggle_password_visibility(self):
        """Toggle password visibility in entry field"""
        if self.show_password_var.get():
            self.password_entry.config(show="")
        else:
            self.password_entry.config(show="*")

    def save_password(self):
        """Save password to data"""
        title = self.title_entry.get().strip()
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        notes = self.notes_text.get("1.0", tk.END).strip()

        if not title or not username or not password:
            messagebox.showerror("Error", "Please fill in Title, Username, and Password")
            return

        # Encode password for basic protection
        encoded_password = base64.b64encode(password.encode()).decode()

        password_data = {
            "title": title,
            "username": username,
            "password": encoded_password,
            "notes": notes
        }

        self.passwords_data.append(password_data)
        self.save_passwords_to_file()
        self.refresh_passwords_list()
        self.clear_password_fields()

        self.status_bar.config(text=f"Password saved: {title}")
        messagebox.showinfo("Success", f"Password for '{title}' saved successfully!")

    def clear_password_fields(self):
        """Clear all password input fields"""
        self.title_entry.delete(0, tk.END)
        self.username_entry.delete(0, tk.END)
        self.password_entry.delete(0, tk.END)
        self.notes_text.delete("1.0", tk.END)
        self.show_password_var.set(False)
        self.toggle_password_visibility()

    def refresh_passwords_list(self):
        """Refresh the passwords treeview"""
        # Clear existing items
        for item in self.passwords_tree.get_children():
            self.passwords_tree.delete(item)

        # Add passwords to treeview
        for i, password in enumerate(self.passwords_data):
            self.passwords_tree.insert("", tk.END, iid=i, values=(
                password["title"],
                password["username"],
                password["notes"][:50] + "..." if len(password["notes"]) > 50 else password["notes"]
            ))

    def copy_username(self):
        """Copy selected username to clipboard"""
        selection = self.passwords_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a password entry")
            return

        index = int(selection[0])
        username = self.passwords_data[index]["username"]

        self.root.clipboard_clear()
        self.root.clipboard_append(username)
        self.root.update()

        self.status_bar.config(text=f"Username copied: {username}")
        messagebox.showinfo("Copied", f"Username '{username}' copied to clipboard!")

    def copy_password(self):
        """Copy selected password to clipboard"""
        selection = self.passwords_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a password entry")
            return

        index = int(selection[0])
        encoded_password = self.passwords_data[index]["password"]

        # Decode password
        try:
            password = base64.b64decode(encoded_password.encode()).decode()
        except:
            password = encoded_password  # Fallback for non-encoded passwords

        self.root.clipboard_clear()
        self.root.clipboard_append(password)
        self.root.update()

        title = self.passwords_data[index]["title"]
        self.status_bar.config(text=f"Password copied for: {title}")
        messagebox.showinfo("Copied", f"Password for '{title}' copied to clipboard!")

    def edit_password(self):
        """Edit selected password"""
        selection = self.passwords_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a password entry to edit")
            return

        index = int(selection[0])
        password_data = self.passwords_data[index]

        # Decode password for editing
        try:
            decoded_password = base64.b64decode(password_data["password"].encode()).decode()
        except:
            decoded_password = password_data["password"]

        # Fill fields with existing data
        self.clear_password_fields()
        self.title_entry.insert(0, password_data["title"])
        self.username_entry.insert(0, password_data["username"])
        self.password_entry.insert(0, decoded_password)
        self.notes_text.insert("1.0", password_data["notes"])

        # Remove the old entry
        del self.passwords_data[index]
        self.save_passwords_to_file()
        self.refresh_passwords_list()

        self.status_bar.config(text=f"Editing: {password_data['title']}")

    def delete_password(self):
        """Delete selected password"""
        selection = self.passwords_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a password entry to delete")
            return

        index = int(selection[0])
        title = self.passwords_data[index]["title"]

        if messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete '{title}'?"):
            del self.passwords_data[index]
            self.save_passwords_to_file()
            self.refresh_passwords_list()
            self.status_bar.config(text=f"Deleted: {title}")

    def save_url(self):
        """Save URL to data"""
        title = self.url_title_entry.get().strip()
        url = self.url_entry.get().strip()
        category = self.url_category_entry.get().strip()
        description = self.url_description_text.get("1.0", tk.END).strip()

        if not title or not url:
            messagebox.showerror("Error", "Please fill in Title and URL")
            return

        # Add http:// if not present
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url

        url_data = {
            "title": title,
            "url": url,
            "category": category,
            "description": description
        }

        self.urls_data.append(url_data)
        self.save_urls_to_file()
        self.refresh_urls_list()
        self.clear_url_fields()

        self.status_bar.config(text=f"URL saved: {title}")
        messagebox.showinfo("Success", f"URL '{title}' saved successfully!")

    def clear_url_fields(self):
        """Clear all URL input fields"""
        self.url_title_entry.delete(0, tk.END)
        self.url_entry.delete(0, tk.END)
        self.url_category_entry.delete(0, tk.END)
        self.url_description_text.delete("1.0", tk.END)

    def refresh_urls_list(self):
        """Refresh the URLs treeview"""
        # Clear existing items
        for item in self.urls_tree.get_children():
            self.urls_tree.delete(item)

        # Add URLs to treeview
        for i, url_data in enumerate(self.urls_data):
            self.urls_tree.insert("", tk.END, iid=i, values=(
                url_data["title"],
                url_data["url"][:40] + "..." if len(url_data["url"]) > 40 else url_data["url"],
                url_data["category"],
                url_data["description"][:30] + "..." if len(url_data["description"]) > 30 else url_data["description"]
            ))

    def open_url(self):
        """Open selected URL in browser"""
        selection = self.urls_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a URL to open")
            return

        index = int(selection[0])
        url = self.urls_data[index]["url"]
        title = self.urls_data[index]["title"]

        try:
            webbrowser.open(url)
            self.status_bar.config(text=f"Opened: {title}")
        except Exception as e:
            messagebox.showerror("Error", f"Could not open URL: {str(e)}")

    def copy_url(self):
        """Copy selected URL to clipboard"""
        selection = self.urls_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a URL to copy")
            return

        index = int(selection[0])
        url = self.urls_data[index]["url"]
        title = self.urls_data[index]["title"]

        self.root.clipboard_clear()
        self.root.clipboard_append(url)
        self.root.update()

        self.status_bar.config(text=f"URL copied: {title}")
        messagebox.showinfo("Copied", f"URL for '{title}' copied to clipboard!")

    def edit_url(self):
        """Edit selected URL"""
        selection = self.urls_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a URL to edit")
            return

        index = int(selection[0])
        url_data = self.urls_data[index]

        # Fill fields with existing data
        self.clear_url_fields()
        self.url_title_entry.insert(0, url_data["title"])
        self.url_entry.insert(0, url_data["url"])
        self.url_category_entry.insert(0, url_data["category"])
        self.url_description_text.insert("1.0", url_data["description"])

        # Remove the old entry
        del self.urls_data[index]
        self.save_urls_to_file()
        self.refresh_urls_list()

        self.status_bar.config(text=f"Editing: {url_data['title']}")

    def delete_url(self):
        """Delete selected URL"""
        selection = self.urls_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a URL to delete")
            return

        index = int(selection[0])
        title = self.urls_data[index]["title"]

        if messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete '{title}'?"):
            del self.urls_data[index]
            self.save_urls_to_file()
            self.refresh_urls_list()
            self.status_bar.config(text=f"Deleted: {title}")

    def choose_background_color(self):
        """Choose background color"""
        color = colorchooser.askcolor(title="Choose Background Color")
        if color[1]:  # If a color was selected
            self.settings["background_color"] = color[1]
            self.color_preview.config(bg=color[1])
            self.apply_settings()
            self.save_settings()
            self.status_bar.config(text=f"Background color changed to: {color[1]}")

    def reset_background_color(self):
        """Reset background color to default"""
        self.settings["background_color"] = "#f0f0f0"
        self.color_preview.config(bg="#f0f0f0")
        self.apply_settings()
        self.save_settings()
        self.status_bar.config(text="Background color reset to default")

    def apply_settings(self):
        """Apply current settings to the application"""
        bg_color = self.settings["background_color"]

        # Apply background color to main frames
        try:
            self.passwords_frame.config(style="Custom.TFrame")
            self.urls_frame.config(style="Custom.TFrame")
            self.settings_frame.config(style="Custom.TFrame")

            # Create custom style
            style = ttk.Style()
            style.configure("Custom.TFrame", background=bg_color)

            # Update color preview if it exists
            if hasattr(self, 'color_preview'):
                self.color_preview.config(bg=bg_color)

        except Exception as e:
            pass  # Ignore styling errors

    def open_data_folder(self):
        """Open the data folder in file explorer"""
        try:
            if os.name == 'nt':  # Windows
                os.startfile(self.desktop_path)
            elif sys.platform == 'darwin':  # macOS
                subprocess.run(['open', self.desktop_path])
            else:  # Linux
                subprocess.run(['xdg-open', self.desktop_path])
            self.status_bar.config(text="Data folder opened")
        except Exception as e:
            messagebox.showerror("Error", f"Could not open data folder: {str(e)}")

    def backup_data(self):
        """Backup data to a chosen location"""
        backup_folder = filedialog.askdirectory(title="Choose Backup Location")
        if not backup_folder:
            return

        try:
            import shutil

            # Copy data files to backup location
            files_to_backup = [
                (self.passwords_file, "passwords_data.json"),
                (self.urls_file, "urls_data.json"),
                (self.settings_file, "app_settings.json")
            ]

            backed_up_files = []
            for source_file, filename in files_to_backup:
                if os.path.exists(source_file):
                    backup_path = os.path.join(backup_folder, filename)
                    shutil.copy2(source_file, backup_path)
                    backed_up_files.append(filename)

            if backed_up_files:
                self.status_bar.config(text=f"Backup completed: {len(backed_up_files)} files")
                messagebox.showinfo("Backup Complete",
                                  f"Backed up files:\n" + "\n".join(backed_up_files))
            else:
                messagebox.showwarning("No Data", "No data files found to backup")

        except Exception as e:
            messagebox.showerror("Backup Error", f"Could not backup data: {str(e)}")

    def import_data(self):
        """Import data from backup files"""
        import_folder = filedialog.askdirectory(title="Choose Import Location")
        if not import_folder:
            return

        try:
            import shutil

            files_to_import = [
                ("passwords_data.json", self.passwords_file),
                ("urls_data.json", self.urls_file),
                ("app_settings.json", self.settings_file)
            ]

            imported_files = []
            for filename, dest_file in files_to_import:
                source_path = os.path.join(import_folder, filename)
                if os.path.exists(source_path):
                    if messagebox.askyesno("Confirm Import",
                                         f"Import {filename}? This will overwrite existing data."):
                        shutil.copy2(source_path, dest_file)
                        imported_files.append(filename)

            if imported_files:
                # Reload data
                self.load_data()
                self.refresh_passwords_list()
                self.refresh_urls_list()
                self.apply_settings()

                self.status_bar.config(text=f"Import completed: {len(imported_files)} files")
                messagebox.showinfo("Import Complete",
                                  f"Imported files:\n" + "\n".join(imported_files))
            else:
                messagebox.showinfo("No Import", "No files were imported")

        except Exception as e:
            messagebox.showerror("Import Error", f"Could not import data: {str(e)}")

    def load_data(self):
        """Load data from JSON files"""
        # Load passwords
        try:
            if os.path.exists(self.passwords_file):
                with open(self.passwords_file, 'r') as f:
                    self.passwords_data = json.load(f)
        except Exception as e:
            self.passwords_data = []
            print(f"Error loading passwords: {e}")

        # Load URLs
        try:
            if os.path.exists(self.urls_file):
                with open(self.urls_file, 'r') as f:
                    self.urls_data = json.load(f)
        except Exception as e:
            self.urls_data = []
            print(f"Error loading URLs: {e}")

        # Load settings
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r') as f:
                    loaded_settings = json.load(f)
                    self.settings.update(loaded_settings)
        except Exception as e:
            print(f"Error loading settings: {e}")

    def save_passwords_to_file(self):
        """Save passwords to JSON file"""
        try:
            with open(self.passwords_file, 'w') as f:
                json.dump(self.passwords_data, f, indent=2)
        except Exception as e:
            messagebox.showerror("Save Error", f"Could not save passwords: {str(e)}")

    def save_urls_to_file(self):
        """Save URLs to JSON file"""
        try:
            with open(self.urls_file, 'w') as f:
                json.dump(self.urls_data, f, indent=2)
        except Exception as e:
            messagebox.showerror("Save Error", f"Could not save URLs: {str(e)}")

    def save_settings(self):
        """Save settings to JSON file"""
        try:
            with open(self.settings_file, 'w') as f:
                json.dump(self.settings, f, indent=2)
        except Exception as e:
            messagebox.showerror("Save Error", f"Could not save settings: {str(e)}")

    def run(self):
        """Start the application"""
        # Load and refresh data
        self.refresh_passwords_list()
        self.refresh_urls_list()

        # Set window icon (if available)
        try:
            self.root.iconname("Password Manager")
        except:
            pass

        # Center window on screen
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

        # Start the main loop
        self.root.mainloop()

def main():
    """Main function"""
    try:
        app = PasswordManager()
        app.run()
    except Exception as e:
        print(f"Error starting application: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()