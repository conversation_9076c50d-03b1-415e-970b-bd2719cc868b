#!/usr/bin/env python3
"""
System Cleanup Script
Clears temporary files from Windows system folders.
Requires Administrator privileges to run.
"""

import os
import sys
import shutil
import ctypes
import getpass
from pathlib import Path

def is_admin():
    """Check if the script is running with administrator privileges"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    """Restart the script with administrator privileges"""
    if is_admin():
        return True
    else:
        print("⚠️  Administrator privileges required!")
        print("Attempting to restart with elevated privileges...")
        try:
            # Re-run the program with admin rights
            ctypes.windll.shell32.ShellExecuteW(
                None, 
                "runas", 
                sys.executable, 
                " ".join(sys.argv), 
                None, 
                1
            )
            return False
        except Exception as e:
            print(f"❌ Failed to elevate privileges: {e}")
            return False

def get_folder_size(folder_path):
    """Calculate the total size of files in a folder"""
    total_size = 0
    try:
        for dirpath, dirnames, filenames in os.walk(folder_path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                try:
                    total_size += os.path.getsize(filepath)
                except (OSError, FileNotFoundError):
                    continue
    except (OSError, FileNotFoundError):
        pass
    return total_size

def format_size(size_bytes):
    """Convert bytes to human readable format"""
    if size_bytes == 0:
        return "0 B"
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    return f"{size_bytes:.2f} {size_names[i]}"

def clear_folder(folder_path, folder_name):
    """Clear all files and subdirectories from a folder"""
    print(f"\n🧹 Cleaning {folder_name}...")
    print(f"   Path: {folder_path}")
    
    if not os.path.exists(folder_path):
        print(f"   ⚠️  Folder does not exist: {folder_path}")
        return 0, 0
    
    # Calculate initial size
    initial_size = get_folder_size(folder_path)
    print(f"   📊 Initial size: {format_size(initial_size)}")
    
    files_deleted = 0
    folders_deleted = 0
    errors = []
    
    try:
        # Get all items in the folder
        items = os.listdir(folder_path)
        
        for item in items:
            item_path = os.path.join(folder_path, item)
            
            try:
                if os.path.isfile(item_path):
                    os.remove(item_path)
                    files_deleted += 1
                elif os.path.isdir(item_path):
                    shutil.rmtree(item_path)
                    folders_deleted += 1
            except PermissionError:
                errors.append(f"Permission denied: {item}")
            except FileNotFoundError:
                errors.append(f"File not found: {item}")
            except Exception as e:
                errors.append(f"Error with {item}: {str(e)}")
    
    except PermissionError:
        print(f"   ❌ Permission denied to access {folder_path}")
        return 0, 0
    except Exception as e:
        print(f"   ❌ Error accessing folder: {e}")
        return 0, 0
    
    # Calculate final size
    final_size = get_folder_size(folder_path)
    space_freed = initial_size - final_size
    
    # Print results
    print(f"   ✅ Files deleted: {files_deleted}")
    print(f"   ✅ Folders deleted: {folders_deleted}")
    print(f"   💾 Space freed: {format_size(space_freed)}")
    
    if errors:
        print(f"   ⚠️  Errors encountered: {len(errors)}")
        for error in errors[:5]:  # Show first 5 errors
            print(f"      - {error}")
        if len(errors) > 5:
            print(f"      ... and {len(errors) - 5} more errors")
    
    return files_deleted, folders_deleted

def main():
    """Main function to perform system cleanup"""
    print("🚀 Windows System Cleanup Script")
    print("=" * 50)
    
    # Check for admin privileges
    if not run_as_admin():
        input("Press Enter to exit...")
        return
    
    print("✅ Running with Administrator privileges")
    
    # Get current username for user temp folder
    username = getpass.getuser()
    
    # Define folders to clean
    folders_to_clean = [
        (r"C:\Windows\Prefetch", "Windows Prefetch"),
        (r"C:\Windows\Temp", "Windows Temp"),
        (rf"C:\Users\<USER>\AppData\Local\Temp", "User Temp")
    ]
    
    print(f"\n📂 Folders to clean:")
    for folder_path, folder_name in folders_to_clean:
        exists = "✅" if os.path.exists(folder_path) else "❌"
        print(f"   {exists} {folder_name}: {folder_path}")
    
    # Ask for confirmation
    print(f"\n⚠️  WARNING: This will delete all files in the above folders!")
    print("   This action cannot be undone.")
    
    while True:
        confirm = input("\nDo you want to continue? (yes/no): ").lower().strip()
        if confirm in ['yes', 'y']:
            break
        elif confirm in ['no', 'n']:
            print("Operation cancelled.")
            input("Press Enter to exit...")
            return
        else:
            print("Please enter 'yes' or 'no'")
    
    # Perform cleanup
    print(f"\n🧹 Starting cleanup process...")
    total_files = 0
    total_folders = 0
    
    for folder_path, folder_name in folders_to_clean:
        files, folders = clear_folder(folder_path, folder_name)
        total_files += files
        total_folders += folders
    
    # Summary
    print(f"\n" + "=" * 50)
    print(f"🎉 Cleanup completed!")
    print(f"📊 Total files deleted: {total_files}")
    print(f"📊 Total folders deleted: {total_folders}")
    print(f"\n💡 Tip: Run this script regularly to keep your system clean.")
    print(f"⚠️  Note: Some files may be in use and cannot be deleted.")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ Operation cancelled by user.")
        input("Press Enter to exit...")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        input("Press Enter to exit...")
