@echo off
title FLUX Text-to-Image Generator
echo.
echo ========================================
echo    FLUX Text-to-Image Generator
echo ========================================
echo.

REM Set the Hugging Face token as environment variable
set HUGGINGFACE_TOKEN=*************************************

echo Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

echo Python found. Starting FLUX generator...
echo.

REM Run the secure version first
python texttoimage_secure.py

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Script execution failed
    echo Trying the original version...
    python texttoimage.py
)

echo.
echo Script completed.
pause
