#!/usr/bin/env python3
"""
Standalone Image Analysis Tool using Google Gemini API
Detects objects, persons, and provides tags for uploaded images.
Auto-installs dependencies and includes both GUI and CLI modes.

Usage:
    python image_analyzer_standalone.py                    # GUI mode
    python image_analyzer_standalone.py image.jpg          # CLI mode
    python image_analyzer_standalone.py --interactive      # Interactive CLI
    python image_analyzer_standalone.py --help             # Show help
"""

import os
import sys
import json
import subprocess
import argparse
from pathlib import Path

def install_package(package_name, import_name=None):
    """Install a package if it's not already installed"""
    if import_name is None:
        import_name = package_name
    
    try:
        __import__(import_name)
        return True
    except ImportError:
        print(f"📦 Installing {package_name}...")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package_name, "--quiet"
            ])
            print(f"✅ Successfully installed {package_name}")
            return True
        except subprocess.CalledProcessError:
            print(f"❌ Failed to install {package_name}")
            print(f"💡 Try manually: pip install {package_name}")
            return False

def check_and_install_dependencies():
    """Check and install all required dependencies"""
    print("🔍 Checking dependencies...")

    dependencies = [
        ("google-generativeai", "google.generativeai"),
        ("Pillow", "PIL"),
        ("matplotlib", "matplotlib"),
        ("opencv-python", "cv2"),
    ]

    all_installed = True
    for package_name, import_name in dependencies:
        if not install_package(package_name, import_name):
            all_installed = False

    # Check tkinter (usually comes with Python)
    try:
        import tkinter
        print("✅ tkinter is available")
    except ImportError:
        print("⚠️  tkinter not available - GUI mode will be disabled")
        print("💡 CLI mode will still work")

    if not all_installed:
        print("❌ Some dependencies failed to install.")
        response = input("Continue anyway? (y/n): ").lower()
        if response != 'y':
            sys.exit(1)
    else:
        print("✅ All dependencies are ready!")

# Install dependencies first
check_and_install_dependencies()

# Import required modules
try:
    import google.generativeai as genai
    from PIL import Image, ImageDraw, ImageFont
    import matplotlib.pyplot as plt
    import matplotlib.patches as patches
    import cv2
    import numpy as np
except ImportError as e:
    print(f"❌ Failed to import required modules: {e}")
    print("💡 Some visualization features may not work")
    # Continue anyway for basic functionality

# Try to import tkinter for GUI
GUI_AVAILABLE = True
try:
    import tkinter as tk
    from tkinter import filedialog, messagebox, scrolledtext
    from tkinter import ttk
except ImportError:
    GUI_AVAILABLE = False
    print("⚠️  GUI mode not available - tkinter not found")

class ImageAnalyzer:
    def __init__(self):
        self.api_key = "AIzaSyBcL4rVwXdB1wzNVOMndNPzCm27SYJo_Co"
        self.setup_gemini()
        
    def setup_gemini(self):
        """Initialize Gemini API"""
        try:
            genai.configure(api_key=self.api_key)
            self.model = genai.GenerativeModel('gemini-1.5-flash')
            print("✅ Gemini API initialized successfully")
        except Exception as e:
            print(f"❌ Failed to initialize Gemini API: {e}")
            print("💡 Please check your internet connection and API key")
            sys.exit(1)
    
    def analyze_image(self, image_path, detailed=False):
        """Analyze image using Gemini API"""
        try:
            print(f"🔍 Analyzing image: {Path(image_path).name}")
            
            if not os.path.exists(image_path):
                return {"error": f"Image file not found: {image_path}"}
            
            # Open image
            image = Image.open(image_path)
            print(f"📏 Image size: {image.size}")
            
            # Create prompt
            prompt = self.get_analysis_prompt(detailed)
            
            print("🤖 Sending request to Gemini API...")
            response = self.model.generate_content([prompt, image])
            
            return self.parse_response(response.text)
            
        except Exception as e:
            return {"error": f"Analysis failed: {str(e)}"}
    
    def get_analysis_prompt(self, detailed=False):
        """Get analysis prompt based on detail level"""
        if detailed:
            return """
            Analyze this image thoroughly and provide comprehensive analysis in JSON format with bounding box coordinates:
            {
                "objects": [
                    {
                        "name": "object_name",
                        "confidence": "high/medium/low",
                        "description": "detailed description",
                        "location": "location in image",
                        "bbox": {"x": 0.1, "y": 0.2, "width": 0.3, "height": 0.4},
                        "size": "relative size",
                        "color": "primary colors"
                    }
                ],
                "persons": [
                    {
                        "count": "number of people",
                        "demographics": "age group, gender if visible",
                        "activities": "what they are doing",
                        "clothing": "clothing description",
                        "location": "position in image",
                        "bbox": {"x": 0.1, "y": 0.2, "width": 0.3, "height": 0.4}
                    }
                ],
                "faces": [
                    {
                        "description": "face description",
                        "expression": "facial expression",
                        "bbox": {"x": 0.1, "y": 0.2, "width": 0.15, "height": 0.2}
                    }
                ],
                "scene": {
                    "setting": "indoor/outdoor/location type",
                    "environment": "environment description",
                    "lighting": "lighting conditions",
                    "mood": "atmosphere and mood"
                },
                "tags": ["comprehensive", "list", "of", "tags"],
                "summary": "Detailed overall description"
            }

            For bbox coordinates, use normalized values (0.0 to 1.0) where:
            - x, y are the top-left corner coordinates
            - width, height are the dimensions
            - (0,0) is top-left, (1,1) is bottom-right of image

            Be thorough and accurate. Only include clearly visible elements with approximate bounding boxes.
            """
        else:
            return """
            Analyze this image and provide concise analysis in JSON format with approximate locations:
            {
                "objects": [
                    {
                        "name": "object_name",
                        "description": "brief description",
                        "bbox": {"x": 0.1, "y": 0.2, "width": 0.3, "height": 0.4}
                    }
                ],
                "persons": [
                    {
                        "count": "number",
                        "description": "brief description",
                        "bbox": {"x": 0.1, "y": 0.2, "width": 0.3, "height": 0.4}
                    }
                ],
                "faces": [
                    {
                        "description": "face description",
                        "bbox": {"x": 0.1, "y": 0.2, "width": 0.15, "height": 0.2}
                    }
                ],
                "scene": {
                    "setting": "indoor/outdoor/etc",
                    "description": "brief scene description"
                },
                "tags": ["relevant", "tags"],
                "summary": "Brief overall description"
            }

            Use normalized bbox coordinates (0.0 to 1.0). Focus on most prominent elements.
            """
    
    def parse_response(self, response_text):
        """Parse Gemini response and extract JSON"""
        try:
            response_text = response_text.strip()
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response_text[start_idx:end_idx]
                return json.loads(json_str)
            else:
                return {
                    "summary": response_text,
                    "objects": [],
                    "persons": [],
                    "scene": {"setting": "unknown", "description": response_text},
                    "tags": [],
                    "raw_response": response_text
                }
        except json.JSONDecodeError:
            return {
                "summary": response_text,
                "objects": [],
                "persons": [],
                "scene": {"setting": "unknown"},
                "tags": [],
                "raw_response": response_text,
                "parse_error": "Could not parse JSON response"
            }

    def create_annotated_image(self, image_path, results, output_path=None):
        """Create an annotated image with bounding boxes and labels"""
        try:
            # Load image
            image = Image.open(image_path)
            img_width, img_height = image.size

            # Create a copy for annotation
            annotated_image = image.copy()
            draw = ImageDraw.Draw(annotated_image)

            # Define colors
            cyan_color = (0, 255, 255)  # Cyan
            text_bg_color = (0, 0, 0, 180)  # Semi-transparent black

            # Try to load a font
            try:
                font = ImageFont.truetype("arial.ttf", 16)
                small_font = ImageFont.truetype("arial.ttf", 12)
            except:
                try:
                    font = ImageFont.load_default()
                    small_font = ImageFont.load_default()
                except:
                    font = None
                    small_font = None

            # Draw objects
            if "objects" in results and results["objects"]:
                for obj in results["objects"]:
                    if "bbox" in obj:
                        self._draw_bbox(draw, obj["bbox"], img_width, img_height,
                                      obj.get("name", "Object"), cyan_color, font)

            # Draw persons
            if "persons" in results and results["persons"]:
                for person in results["persons"]:
                    if "bbox" in person:
                        label = f"Person ({person.get('count', '1')})"
                        self._draw_bbox(draw, person["bbox"], img_width, img_height,
                                      label, cyan_color, font)

            # Draw faces
            if "faces" in results and results["faces"]:
                for face in results["faces"]:
                    if "bbox" in face:
                        label = f"Face ({face.get('expression', 'neutral')})"
                        self._draw_bbox(draw, face["bbox"], img_width, img_height,
                                      label, cyan_color, font, thickness=2)

            # Add tags overlay
            if "tags" in results and results["tags"]:
                self._draw_tags_overlay(draw, results["tags"], img_width, img_height,
                                      cyan_color, small_font)

            # Save or return annotated image
            if output_path:
                annotated_image.save(output_path)
                print(f"💾 Annotated image saved: {output_path}")

            return annotated_image

        except Exception as e:
            print(f"❌ Failed to create annotated image: {e}")
            return None

    def _draw_bbox(self, draw, bbox, img_width, img_height, label, color, font, thickness=3):
        """Draw a bounding box with label"""
        try:
            # Convert normalized coordinates to pixel coordinates
            x = int(bbox["x"] * img_width)
            y = int(bbox["y"] * img_height)
            width = int(bbox["width"] * img_width)
            height = int(bbox["height"] * img_height)

            # Draw rectangle
            for i in range(thickness):
                draw.rectangle([x-i, y-i, x+width+i, y+height+i], outline=color, width=1)

            # Draw label background
            if font:
                text_bbox = draw.textbbox((0, 0), label, font=font)
                text_width = text_bbox[2] - text_bbox[0]
                text_height = text_bbox[3] - text_bbox[1]
            else:
                text_width, text_height = len(label) * 8, 16

            # Draw text background
            draw.rectangle([x, y-text_height-4, x+text_width+8, y], fill=(0, 0, 0, 180))

            # Draw text
            if font:
                draw.text((x+4, y-text_height-2), label, fill=color, font=font)
            else:
                draw.text((x+4, y-text_height-2), label, fill=color)

        except Exception as e:
            print(f"⚠️  Could not draw bbox for {label}: {e}")

    def _draw_tags_overlay(self, draw, tags, img_width, img_height, color, font):
        """Draw tags overlay at the bottom of the image"""
        try:
            # Prepare tags text
            tags_text = "Tags: " + ", ".join(tags[:10])  # Limit to first 10 tags

            # Calculate text dimensions
            if font:
                text_bbox = draw.textbbox((0, 0), tags_text, font=font)
                text_width = text_bbox[2] - text_bbox[0]
                text_height = text_bbox[3] - text_bbox[1]
            else:
                text_width, text_height = len(tags_text) * 6, 12

            # Position at bottom of image
            x = 10
            y = img_height - text_height - 20

            # Draw background
            draw.rectangle([x-5, y-5, x+text_width+10, y+text_height+5],
                         fill=(0, 0, 0, 180))

            # Draw text
            if font:
                draw.text((x, y), tags_text, fill=color, font=font)
            else:
                draw.text((x, y), tags_text, fill=color)

        except Exception as e:
            print(f"⚠️  Could not draw tags overlay: {e}")

    def display_image_with_matplotlib(self, image_path, results):
        """Display annotated image using matplotlib"""
        try:
            annotated_image = self.create_annotated_image(image_path, results)
            if annotated_image:
                plt.figure(figsize=(12, 8))
                plt.imshow(annotated_image)
                plt.axis('off')
                plt.title('Image Analysis Results', fontsize=16, color='cyan')
                plt.tight_layout()
                plt.show()
        except Exception as e:
            print(f"❌ Could not display image: {e}")

class CLIInterface:
    def __init__(self):
        self.analyzer = ImageAnalyzer()
    
    def display_results(self, results, output_format="text", image_path=None, show_image=False):
        """Display results in specified format"""
        if output_format == "json":
            print(json.dumps(results, indent=2))
            return

        if "error" in results:
            print(f"\n❌ Error: {results['error']}")
            return

        print("\n" + "="*60)
        print("🎯 IMAGE ANALYSIS RESULTS")
        print("="*60)

        if "summary" in results and results["summary"]:
            print(f"\n📋 SUMMARY:")
            print(f"   {results['summary']}")

        if "objects" in results and results["objects"]:
            print(f"\n🎯 OBJECTS DETECTED ({len(results['objects'])}):")
            for i, obj in enumerate(results["objects"], 1):
                print(f"   {i}. {obj.get('name', 'Unknown')}")
                for key, value in obj.items():
                    if key != 'name' and key != 'bbox' and value:
                        print(f"      {key.title()}: {value}")

        if "persons" in results and results["persons"]:
            print(f"\n👥 PERSONS DETECTED:")
            for person in results["persons"]:
                for key, value in person.items():
                    if key != 'bbox' and value:
                        print(f"   {key.title()}: {value}")

        if "faces" in results and results["faces"]:
            print(f"\n😊 FACES DETECTED ({len(results['faces'])}):")
            for i, face in enumerate(results["faces"], 1):
                print(f"   {i}. {face.get('description', 'Face')}")
                if 'expression' in face:
                    print(f"      Expression: {face['expression']}")

        if "scene" in results and results["scene"]:
            print(f"\n🏞️ SCENE INFORMATION:")
            for key, value in results["scene"].items():
                if value:
                    print(f"   {key.title()}: {value}")

        if "tags" in results and results["tags"]:
            print(f"\n🏷️ TAGS ({len(results['tags'])}):")
            tags_str = ", ".join(results["tags"])
            print(f"   {tags_str}")

        print("\n" + "="*60)

        # Show annotated image if requested
        if show_image and image_path:
            try:
                print("\n🖼️  Displaying annotated image...")
                self.analyzer.display_image_with_matplotlib(image_path, results)
            except Exception as e:
                print(f"⚠️  Could not display image: {e}")

        # Save annotated image
        if image_path and ("objects" in results or "persons" in results or "faces" in results):
            try:
                output_path = self._get_annotated_filename(image_path)
                self.analyzer.create_annotated_image(image_path, results, output_path)
            except Exception as e:
                print(f"⚠️  Could not save annotated image: {e}")

    def _get_annotated_filename(self, image_path):
        """Generate filename for annotated image"""
        path = Path(image_path)
        return str(path.parent / f"{path.stem}_annotated{path.suffix}")
    
    def interactive_mode(self):
        """Interactive CLI mode"""
        print("\n🔧 Interactive Mode - Enter image paths to analyze")
        print("Type 'quit' or 'exit' to stop")

        while True:
            image_path = input("\n📁 Enter image path: ").strip()
            if image_path.lower() in ['quit', 'exit', 'q']:
                break

            if not image_path:
                continue

            if not os.path.exists(image_path):
                print(f"❌ File not found: {image_path}")
                continue

            detailed = input("Detailed analysis? (y/n): ").lower().startswith('y')
            show_image = input("Show annotated image? (y/n): ").lower().startswith('y')

            results = self.analyzer.analyze_image(image_path, detailed)
            self.display_results(results, image_path=image_path, show_image=show_image)

# GUI Interface (only if tkinter is available)
if GUI_AVAILABLE:
    class GUIInterface:
        def __init__(self):
            self.analyzer = ImageAnalyzer()
            self.setup_gui()
            
        def setup_gui(self):
            """Setup GUI interface"""
            self.root = tk.Tk()
            self.root.title("Image Analyzer - Object & Person Detection")
            self.root.geometry("1000x700")

            main_frame = ttk.Frame(self.root, padding="10")
            main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

            # Title
            title_label = ttk.Label(main_frame, text="🔍 Image Analysis Tool",
                                   font=("Arial", 16, "bold"))
            title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))

            # Upload button
            self.upload_btn = ttk.Button(main_frame, text="📁 Upload Image",
                                        command=self.upload_image)
            self.upload_btn.grid(row=1, column=0, sticky=tk.W, pady=(0, 10))

            # File label
            self.file_label = ttk.Label(main_frame, text="No file selected")
            self.file_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=(0, 10))

            # Options frame
            options_frame = ttk.Frame(main_frame)
            options_frame.grid(row=2, column=0, columnspan=3, sticky=tk.W, pady=(0, 10))

            self.detailed_var = tk.BooleanVar()
            detailed_check = ttk.Checkbutton(options_frame, text="Detailed Analysis",
                                           variable=self.detailed_var)
            detailed_check.grid(row=0, column=0, sticky=tk.W, padx=(0, 20))

            self.show_image_var = tk.BooleanVar(value=True)
            show_image_check = ttk.Checkbutton(options_frame, text="Show Annotated Image",
                                             variable=self.show_image_var)
            show_image_check.grid(row=0, column=1, sticky=tk.W)

            # Buttons frame
            buttons_frame = ttk.Frame(main_frame)
            buttons_frame.grid(row=3, column=0, columnspan=3, sticky=tk.W, pady=(0, 20))

            # Analyze button
            self.analyze_btn = ttk.Button(buttons_frame, text="🔍 Analyze Image",
                                         command=self.analyze_image, state="disabled")
            self.analyze_btn.grid(row=0, column=0, sticky=tk.W, padx=(0, 10))

            # Show image button
            self.show_img_btn = ttk.Button(buttons_frame, text="🖼️ Show Annotated Image",
                                          command=self.show_annotated_image, state="disabled")
            self.show_img_btn.grid(row=0, column=1, sticky=tk.W)

            # Progress bar
            self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
            self.progress.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

            # Results
            results_label = ttk.Label(main_frame, text="Analysis Results:",
                                     font=("Arial", 12, "bold"))
            results_label.grid(row=5, column=0, sticky=tk.W, pady=(10, 5))

            self.results_text = scrolledtext.ScrolledText(main_frame, height=20, width=80)
            self.results_text.grid(row=6, column=0, columnspan=3,
                                  sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

            # Configure grid weights
            self.root.columnconfigure(0, weight=1)
            self.root.rowconfigure(0, weight=1)
            main_frame.columnconfigure(2, weight=1)
            main_frame.rowconfigure(6, weight=1)

            self.selected_file = None
            self.current_results = None
        
        def upload_image(self):
            """Handle image upload"""
            file_types = [
                ("Image files", "*.jpg *.jpeg *.png *.bmp *.gif *.tiff"),
                ("All files", "*.*")
            ]
            
            filename = filedialog.askopenfilename(
                title="Select an image file",
                filetypes=file_types
            )
            
            if filename:
                self.selected_file = filename
                self.file_label.config(text=f"Selected: {Path(filename).name}")
                self.analyze_btn.config(state="normal")
                self.results_text.delete(1.0, tk.END)
        
        def analyze_image(self):
            """Analyze selected image"""
            if not self.selected_file:
                messagebox.showerror("Error", "Please select an image first")
                return

            self.progress.start()
            self.analyze_btn.config(state="disabled")
            self.show_img_btn.config(state="disabled")
            self.root.update()

            try:
                results = self.analyzer.analyze_image(
                    self.selected_file,
                    self.detailed_var.get()
                )
                self.current_results = results
                self.display_results(results)

                # Enable show image button if analysis successful
                if not "error" in results:
                    self.show_img_btn.config(state="normal")

                    # Auto-show image if option is checked
                    if self.show_image_var.get():
                        self.show_annotated_image()

            except Exception as e:
                messagebox.showerror("Error", f"Analysis failed: {str(e)}")
            finally:
                self.progress.stop()
                self.analyze_btn.config(state="normal")

        def show_annotated_image(self):
            """Show the annotated image"""
            if not self.selected_file or not self.current_results:
                messagebox.showerror("Error", "Please analyze an image first")
                return

            try:
                self.analyzer.display_image_with_matplotlib(self.selected_file, self.current_results)
            except Exception as e:
                messagebox.showerror("Error", f"Could not display image: {str(e)}")
        
        def display_results(self, results):
            """Display results in GUI"""
            self.results_text.delete(1.0, tk.END)
            
            if "error" in results:
                self.results_text.insert(tk.END, f"❌ Error: {results['error']}\n\n")
                if "raw_response" in results:
                    self.results_text.insert(tk.END, f"Raw Response:\n{results['raw_response']}")
                return
            
            output = []
            output.append("🎯 IMAGE ANALYSIS RESULTS")
            output.append("=" * 50)
            
            if "summary" in results and results["summary"]:
                output.append(f"\n📋 SUMMARY:")
                output.append(f"{results['summary']}")
            
            if "objects" in results and results["objects"]:
                output.append(f"\n🎯 OBJECTS DETECTED ({len(results['objects'])}):")
                for i, obj in enumerate(results["objects"], 1):
                    output.append(f"  {i}. {obj.get('name', 'Unknown')}")
                    for key, value in obj.items():
                        if key != 'name' and value:
                            output.append(f"     {key.title()}: {value}")
                    output.append("")
            
            if "persons" in results and results["persons"]:
                output.append(f"\n👥 PERSONS DETECTED:")
                for person in results["persons"]:
                    for key, value in person.items():
                        if key != 'bbox' and value:
                            output.append(f"  {key.title()}: {value}")
                    output.append("")

            if "faces" in results and results["faces"]:
                output.append(f"\n😊 FACES DETECTED ({len(results['faces'])}):")
                for i, face in enumerate(results["faces"], 1):
                    output.append(f"  {i}. {face.get('description', 'Face')}")
                    if 'expression' in face:
                        output.append(f"     Expression: {face['expression']}")
                    output.append("")

            if "scene" in results and results["scene"]:
                output.append(f"\n🏞️ SCENE INFORMATION:")
                for key, value in results["scene"].items():
                    if value:
                        output.append(f"  {key.title()}: {value}")
                output.append("")

            if "tags" in results and results["tags"]:
                output.append(f"\n🏷️ TAGS ({len(results['tags'])}):")
                tags_str = ", ".join(results["tags"])
                output.append(f"  {tags_str}")

            # Add info about annotated image
            if ("objects" in results and results["objects"]) or \
               ("persons" in results and results["persons"]) or \
               ("faces" in results and results["faces"]):
                output.append(f"\n🖼️ ANNOTATED IMAGE:")
                output.append(f"  • Cyan boxes highlight detected objects/persons/faces")
                output.append(f"  • Tags are shown at the bottom of the image")
                output.append(f"  • Annotated image saved automatically")

            self.results_text.insert(tk.END, "\n".join(output))
        
        def run(self):
            """Start GUI"""
            self.root.mainloop()

def main():
    """Main function with argument parsing"""
    parser = argparse.ArgumentParser(
        description="Image Analysis Tool using Google Gemini API",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python image_analyzer_standalone.py                    # GUI mode
    python image_analyzer_standalone.py image.jpg          # Analyze single image
    python image_analyzer_standalone.py image.jpg -d       # Detailed analysis
    python image_analyzer_standalone.py -i                 # Interactive mode
    python image_analyzer_standalone.py image.jpg -f json  # JSON output
        """
    )
    
    parser.add_argument("image", nargs='?', help="Path to image file")
    parser.add_argument("-d", "--detailed", action="store_true", 
                       help="Perform detailed analysis")
    parser.add_argument("-f", "--format", choices=["text", "json"], default="text",
                       help="Output format (default: text)")
    parser.add_argument("-i", "--interactive", action="store_true",
                       help="Interactive mode")
    parser.add_argument("--cli", action="store_true",
                       help="Force CLI mode (skip GUI)")
    parser.add_argument("--show-image", action="store_true",
                       help="Display annotated image (CLI mode)")
    parser.add_argument("--no-save", action="store_true",
                       help="Don't save annotated image")
    
    args = parser.parse_args()
    
    # Determine mode
    if args.interactive:
        # Interactive CLI mode
        cli = CLIInterface()
        cli.interactive_mode()
    elif args.image:
        # Single image CLI mode
        if not os.path.exists(args.image):
            print(f"❌ Image file not found: {args.image}")
            sys.exit(1)

        cli = CLIInterface()
        analyzer = ImageAnalyzer()
        results = analyzer.analyze_image(args.image, args.detailed)
        cli.display_results(results, args.format, image_path=args.image,
                           show_image=args.show_image)
    elif args.cli or not GUI_AVAILABLE:
        # CLI mode (forced or GUI not available)
        cli = CLIInterface()
        cli.interactive_mode()
    else:
        # GUI mode (default)
        if GUI_AVAILABLE:
            gui = GUIInterface()
            gui.run()
        else:
            print("❌ GUI not available. Starting CLI mode...")
            cli = CLIInterface()
            cli.interactive_mode()

if __name__ == "__main__":
    print("🚀 Image Analyzer - Standalone Version")
    print("Auto-installing dependencies...")
    main()
