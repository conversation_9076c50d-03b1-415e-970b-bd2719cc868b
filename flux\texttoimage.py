import torch
from diffusers import FluxPipeline
from diffusers import DiffusionPipeline
from huggingface_hub import login

# Multiple authentication methods
def authenticate_huggingface():
    token = "*************************************"

    try:
        # Method 1: Login first (recommended)
        login(token=token)
        print("✅ Successfully authenticated with Hugging Face")
        return True
    except Exception as e:
        print(f"❌ Authentication failed: {e}")
        return False

# Authenticate first
if not authenticate_huggingface():
    print("Please check your token and try again")
    exit(1)

# Load the pipeline
try:
    pipeline = DiffusionPipeline.from_pretrained(
        "black-forest-labs/FLUX.1-dev",
        token="*************************************",
        torch_dtype=torch.bfloat16
    )
    print("✅ Pipeline loaded successfully")
except Exception as e:
    print(f"❌ Failed to load pipeline: {e}")
    exit(1)


# Generate image
prompt = "A cat holding a sign that says hello world"
print(f"🎨 Generating image with prompt: '{prompt}'")

try:
    image = pipeline(
        prompt,
        height=1024,
        width=1024,
        guidance_scale=3.5,
        num_inference_steps=50,
        max_sequence_length=512,
        generator=torch.Generator("cpu").manual_seed(0)
    ).images[0]

    # Save the image
    output_filename = "flux-dev.png"
    image.save(output_filename)
    print(f"✅ Image saved successfully as '{output_filename}'")

except Exception as e:
    print(f"❌ Failed to generate image: {e}")
    exit(1)
