# Image Analyzer - Object & Person Detection

A comprehensive Python tool that uses Google's Gemini API to analyze images, detect objects and persons, and provide detailed tags and descriptions.

## 🎯 Features

- **Object Detection**: Identifies and describes objects in images
- **Person Detection**: Counts people and describes their activities
- **Scene Analysis**: Analyzes environment, lighting, and mood
- **Tagging System**: Generates relevant tags for images
- **Multiple Interfaces**: GUI and Command Line options
- **Batch Processing**: Analyze multiple images at once
- **Detailed Analysis**: Optional comprehensive analysis mode

## 📁 Files Included

1. **`image_analyzer.py`** - GUI version with user-friendly interface
2. **`image_analyzer_cli.py`** - Command line version with advanced options
3. **`run_image_analyzer.bat`** - Easy-to-use batch file launcher
4. **`image_analyzer_requirements.txt`** - Python dependencies
5. **`IMAGE_ANALYZER_README.md`** - This documentation

## 🚀 Installation & Setup

### Prerequisites
- Python 3.7 or higher
- Internet connection (for Gemini API)

### Quick Setup
1. **Run the batch file** (easiest method):
   ```
   Double-click: run_image_analyzer.bat
   ```

2. **Manual installation**:
   ```bash
   pip install -r image_analyzer_requirements.txt
   ```

### Dependencies
- `google-generativeai` - Google Gemini API client
- `Pillow` - Image processing
- `tkinter` - GUI interface (usually included with Python)

## 🖥️ Usage Options

### Option 1: GUI Mode (Recommended for beginners)
```bash
python image_analyzer.py
```

**Features:**
- User-friendly graphical interface
- Drag & drop image upload
- Real-time progress indication
- Formatted results display
- Easy to use for non-technical users

### Option 2: Command Line Mode
```bash
# Basic analysis
python image_analyzer_cli.py image.jpg

# Detailed analysis
python image_analyzer_cli.py image.jpg --detailed

# JSON output
python image_analyzer_cli.py image.jpg --format json

# Interactive mode
python image_analyzer_cli.py --interactive

# Batch processing
python image_analyzer_cli.py folder_path --batch --output results.json
```

### Option 3: Batch File Launcher
```
Double-click: run_image_analyzer.bat
```
Choose from GUI, CLI, or Interactive modes.

## 📊 Analysis Output

### Objects Detected
```json
{
  "objects": [
    {
      "name": "car",
      "confidence": "high",
      "description": "red sedan parked on street",
      "location": "center-left of image",
      "size": "medium",
      "color": "red"
    }
  ]
}
```

### Persons Detected
```json
{
  "persons": [
    {
      "count": "2",
      "demographics": "adults, mixed gender",
      "activities": "walking together",
      "clothing": "casual wear",
      "location": "foreground"
    }
  ]
}
```

### Scene Information
```json
{
  "scene": {
    "setting": "outdoor",
    "environment": "urban street with buildings",
    "lighting": "natural daylight",
    "weather": "clear sky",
    "mood": "peaceful, everyday life"
  }
}
```

### Tags
```json
{
  "tags": [
    "street", "urban", "people", "car", "daylight", 
    "walking", "city", "buildings", "outdoor"
  ]
}
```

## 🔧 Command Line Options

| Option | Description | Example |
|--------|-------------|---------|
| `image_path` | Path to image file | `python image_analyzer_cli.py photo.jpg` |
| `--detailed` | Comprehensive analysis | `--detailed` |
| `--format` | Output format (text/json) | `--format json` |
| `--batch` | Process folder of images | `--batch` |
| `--output` | Save results to file | `--output results.json` |
| `--interactive` | Interactive mode | `--interactive` |

## 📷 Supported Image Formats

- **JPEG** (.jpg, .jpeg)
- **PNG** (.png)
- **BMP** (.bmp)
- **GIF** (.gif)
- **TIFF** (.tiff)

## 🎨 Example Usage Scenarios

### 1. Social Media Content Analysis
```bash
python image_analyzer_cli.py social_media_post.jpg --detailed
```
Get detailed tags and descriptions for social media posts.

### 2. Photo Organization
```bash
python image_analyzer_cli.py photos_folder --batch --output photo_tags.json
```
Automatically tag and organize large photo collections.

### 3. Security/Surveillance Analysis
```bash
python image_analyzer_cli.py security_camera.jpg --format json
```
Analyze security footage for objects and people.

### 4. E-commerce Product Analysis
```bash
python image_analyzer_cli.py product_image.jpg --detailed --format json
```
Generate product descriptions and tags automatically.

## 🔍 Analysis Capabilities

### What It Can Detect:
- ✅ **Objects**: Cars, furniture, electronics, food, etc.
- ✅ **People**: Count, activities, approximate age groups
- ✅ **Animals**: Pets, wildlife, livestock
- ✅ **Scenes**: Indoor/outdoor, urban/rural, time of day
- ✅ **Activities**: Sports, work, leisure activities
- ✅ **Emotions**: Facial expressions, mood assessment
- ✅ **Text**: Signs, labels, written content in images

### Limitations:
- ❌ Cannot identify specific individuals by name
- ❌ May struggle with very low-quality images
- ❌ Cannot detect objects smaller than ~5% of image
- ❌ Accuracy depends on image clarity and lighting

## 🛡️ Privacy & Security

- **API Key**: Hardcoded in script (change for production use)
- **Data**: Images are sent to Google's Gemini API for processing
- **Storage**: No images are stored locally by the tool
- **Logs**: No analysis results are logged unless explicitly saved

### For Production Use:
```python
# Use environment variable for API key
import os
api_key = os.getenv('GEMINI_API_KEY')
```

## 🔧 Troubleshooting

### Common Issues:

#### "API Key Error"
- **Cause**: Invalid or expired Gemini API key
- **Solution**: Update the API key in the script

#### "Image Not Found"
- **Cause**: Incorrect file path or unsupported format
- **Solution**: Check file path and format

#### "Connection Error"
- **Cause**: No internet connection
- **Solution**: Check internet connectivity

#### "Import Error"
- **Cause**: Missing dependencies
- **Solution**: Run `pip install -r image_analyzer_requirements.txt`

### Performance Tips:
- **Large Images**: Resize images >2MB for faster processing
- **Batch Processing**: Use CLI mode for multiple images
- **Network**: Stable internet connection improves reliability

## 📈 Advanced Features

### Batch Processing Example:
```bash
# Analyze all images in a folder
python image_analyzer_cli.py ./photos --batch --output analysis_results.json

# Process with detailed analysis
python image_analyzer_cli.py ./photos --batch --detailed --output detailed_results.json
```

### Custom Analysis:
Modify the prompts in the code to focus on specific types of detection:
- Medical image analysis
- Art and design analysis
- Technical diagram analysis
- Document analysis

## 🆘 Support & Help

### Getting Help:
1. **Check this README** for common solutions
2. **Run with verbose output** to see detailed error messages
3. **Test with sample images** to verify setup
4. **Check Google Gemini API status** if connection issues persist

### Example Test Command:
```bash
python image_analyzer_cli.py test_image.jpg --detailed --format json
```

---

**🎉 Happy Analyzing!** This tool makes image analysis accessible and powerful for various use cases.
