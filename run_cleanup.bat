@echo off
title Windows System Cleanup
echo.
echo ========================================
echo    Windows System Cleanup Script
echo ========================================
echo.
echo This script will clean temporary files from:
echo - C:\Windows\Prefetch
echo - C:\Windows\Temp  
echo - C:\Users\<USER>\AppData\Local\Temp
echo.
echo Checking for Python installation...

python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

echo Python found. Starting cleanup script...
echo.

python "%~dp0system_cleanup.py"

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Script execution failed
    pause
)

echo.
echo Script completed.
pause
