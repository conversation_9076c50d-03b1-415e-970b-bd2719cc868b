#!/usr/bin/env python3
"""
Command Line Image Analysis Tool using Google Gemini API
Detects objects, persons, and provides tags for images.
Auto-installs required dependencies.
"""

import os
import sys
import json
import argparse
import subprocess
from pathlib import Path

def install_package(package_name, import_name=None):
    """Install a package if it's not already installed"""
    if import_name is None:
        import_name = package_name

    try:
        __import__(import_name)
        return True
    except ImportError:
        print(f"📦 Installing {package_name}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
            print(f"✅ Successfully installed {package_name}")
            return True
        except subprocess.CalledProcessError:
            print(f"❌ Failed to install {package_name}")
            return False

def check_and_install_dependencies():
    """Check and install all required dependencies"""
    print("🔍 Checking dependencies...")

    dependencies = [
        ("google-generativeai", "google.generativeai"),
        ("Pillow", "PIL"),
    ]

    all_installed = True
    for package_name, import_name in dependencies:
        if not install_package(package_name, import_name):
            all_installed = False

    if all_installed:
        print("✅ All dependencies are ready!")
    else:
        print("❌ Some dependencies failed to install. Please install them manually.")
        sys.exit(1)

# Install dependencies before importing them
check_and_install_dependencies()

# Now import the required modules
import google.generativeai as genai
from PIL import Image

class ImageAnalyzerCLI:
    def __init__(self):
        self.api_key = "AIzaSyBcL4rVwXdB1wzNVOMndNPzCm27SYJo_Co"
        self.setup_gemini()
        
    def setup_gemini(self):
        """Initialize Gemini API"""
        try:
            genai.configure(api_key=self.api_key)
            self.model = genai.GenerativeModel('gemini-1.5-flash')
            print("✅ Gemini API initialized successfully")
        except Exception as e:
            print(f"❌ Failed to initialize Gemini API: {e}")
            sys.exit(1)
    
    def analyze_image(self, image_path, detailed=False):
        """Analyze image using Gemini API"""
        try:
            print(f"🔍 Analyzing image: {image_path}")
            
            # Verify image exists
            if not os.path.exists(image_path):
                return {"error": f"Image file not found: {image_path}"}
            
            # Open and prepare image
            image = Image.open(image_path)
            print(f"📏 Image size: {image.size}")
            
            # Create prompt based on detail level
            if detailed:
                prompt = self.get_detailed_prompt()
            else:
                prompt = self.get_simple_prompt()
            
            print("🤖 Sending request to Gemini API...")
            
            # Generate content
            response = self.model.generate_content([prompt, image])
            
            return self.parse_response(response.text)
            
        except Exception as e:
            return {"error": f"Analysis failed: {str(e)}"}
    
    def get_detailed_prompt(self):
        """Get detailed analysis prompt"""
        return """
        Analyze this image thoroughly and provide a comprehensive analysis in JSON format:
        {
            "objects": [
                {
                    "name": "object_name",
                    "confidence": "high/medium/low",
                    "description": "detailed description",
                    "location": "specific location in image",
                    "size": "relative size (small/medium/large)",
                    "color": "primary colors"
                }
            ],
            "persons": [
                {
                    "count": "exact number of people visible",
                    "demographics": "age group, gender (if clearly visible)",
                    "activities": "what they are doing",
                    "clothing": "description of clothing",
                    "poses": "body positions/poses",
                    "location": "where they are positioned"
                }
            ],
            "scene": {
                "setting": "indoor/outdoor/specific location type",
                "environment": "detailed environment description",
                "lighting": "lighting conditions and quality",
                "weather": "weather conditions if outdoor",
                "time_of_day": "apparent time of day",
                "mood": "overall atmosphere and mood"
            },
            "technical": {
                "image_quality": "assessment of image quality",
                "composition": "photographic composition notes",
                "colors": "dominant color palette",
                "style": "photographic or artistic style"
            },
            "tags": [
                "comprehensive", "list", "of", "relevant", "tags"
            ],
            "summary": "Detailed overall description of the image"
        }
        
        Be extremely thorough and accurate. Only include what you can clearly see.
        """
    
    def get_simple_prompt(self):
        """Get simple analysis prompt"""
        return """
        Analyze this image and provide a concise analysis in JSON format:
        {
            "objects": [
                {"name": "object_name", "description": "brief description"}
            ],
            "persons": [
                {"count": "number", "description": "brief description"}
            ],
            "scene": {
                "setting": "indoor/outdoor/etc",
                "description": "brief scene description"
            },
            "tags": ["relevant", "tags"],
            "summary": "Brief overall description"
        }
        
        Focus on the most prominent and clearly visible elements.
        """
    
    def parse_response(self, response_text):
        """Parse Gemini response and extract JSON"""
        try:
            # Clean up the response text
            response_text = response_text.strip()
            
            # Try to find JSON in the response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response_text[start_idx:end_idx]
                return json.loads(json_str)
            else:
                # If no JSON found, create structured response from text
                return {
                    "summary": response_text,
                    "objects": [],
                    "persons": [],
                    "scene": {"setting": "unknown", "description": response_text},
                    "tags": [],
                    "raw_response": response_text
                }
        except json.JSONDecodeError as e:
            return {
                "summary": response_text,
                "objects": [],
                "persons": [],
                "scene": {"setting": "unknown"},
                "tags": [],
                "raw_response": response_text,
                "parse_error": str(e)
            }
    
    def display_results(self, results, output_format="text"):
        """Display results in specified format"""
        if output_format == "json":
            print(json.dumps(results, indent=2))
            return
        
        # Text format display
        if "error" in results:
            print(f"\n❌ Error: {results['error']}")
            return
        
        print("\n" + "="*60)
        print("🎯 IMAGE ANALYSIS RESULTS")
        print("="*60)
        
        # Summary
        if "summary" in results and results["summary"]:
            print(f"\n📋 SUMMARY:")
            print(f"   {results['summary']}")
        
        # Objects
        if "objects" in results and results["objects"]:
            print(f"\n🎯 OBJECTS DETECTED ({len(results['objects'])}):")
            for i, obj in enumerate(results["objects"], 1):
                print(f"   {i}. {obj.get('name', 'Unknown')}")
                for key, value in obj.items():
                    if key != 'name' and value:
                        print(f"      {key.title()}: {value}")
        
        # Persons
        if "persons" in results and results["persons"]:
            print(f"\n👥 PERSONS DETECTED:")
            for person in results["persons"]:
                for key, value in person.items():
                    if value:
                        print(f"   {key.title()}: {value}")
        
        # Scene
        if "scene" in results and results["scene"]:
            print(f"\n🏞️ SCENE INFORMATION:")
            for key, value in results["scene"].items():
                if value:
                    print(f"   {key.title()}: {value}")
        
        # Technical details (if available)
        if "technical" in results and results["technical"]:
            print(f"\n🔧 TECHNICAL DETAILS:")
            for key, value in results["technical"].items():
                if value:
                    print(f"   {key.title()}: {value}")
        
        # Tags
        if "tags" in results and results["tags"]:
            print(f"\n🏷️ TAGS ({len(results['tags'])}):")
            tags_str = ", ".join(results["tags"])
            print(f"   {tags_str}")
        
        print("\n" + "="*60)
    
    def batch_analyze(self, image_folder, output_file=None):
        """Analyze multiple images in a folder"""
        folder_path = Path(image_folder)
        if not folder_path.exists():
            print(f"❌ Folder not found: {image_folder}")
            return
        
        # Supported image extensions
        extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff'}
        image_files = [f for f in folder_path.iterdir() 
                      if f.suffix.lower() in extensions]
        
        if not image_files:
            print(f"❌ No image files found in: {image_folder}")
            return
        
        print(f"📁 Found {len(image_files)} images to analyze")
        
        results = {}
        for i, image_file in enumerate(image_files, 1):
            print(f"\n[{i}/{len(image_files)}] Processing: {image_file.name}")
            result = self.analyze_image(str(image_file))
            results[image_file.name] = result
        
        # Save results if output file specified
        if output_file:
            with open(output_file, 'w') as f:
                json.dump(results, f, indent=2)
            print(f"\n💾 Results saved to: {output_file}")
        
        return results

def main():
    """Main function with argument parsing"""
    parser = argparse.ArgumentParser(description="Image Analysis Tool using Google Gemini API")
    parser.add_argument("image", nargs='?', help="Path to image file or folder")
    parser.add_argument("-d", "--detailed", action="store_true", 
                       help="Perform detailed analysis")
    parser.add_argument("-f", "--format", choices=["text", "json"], default="text",
                       help="Output format (default: text)")
    parser.add_argument("-b", "--batch", action="store_true",
                       help="Batch process all images in folder")
    parser.add_argument("-o", "--output", help="Output file for batch processing")
    parser.add_argument("-i", "--interactive", action="store_true",
                       help="Interactive mode - prompt for image path")
    
    args = parser.parse_args()
    
    analyzer = ImageAnalyzerCLI()
    
    # Interactive mode
    if args.interactive:
        while True:
            image_path = input("\n📁 Enter image path (or 'quit' to exit): ").strip()
            if image_path.lower() in ['quit', 'exit', 'q']:
                break
            
            if not image_path:
                continue
                
            results = analyzer.analyze_image(image_path, args.detailed)
            analyzer.display_results(results, args.format)
        return
    
    # Check if image path provided
    if not args.image:
        print("❌ Please provide an image path or use --interactive mode")
        print("Usage: python image_analyzer_cli.py <image_path>")
        sys.exit(1)
    
    # Batch processing
    if args.batch:
        analyzer.batch_analyze(args.image, args.output)
        return
    
    # Single image analysis
    if not os.path.exists(args.image):
        print(f"❌ Image file not found: {args.image}")
        sys.exit(1)
    
    results = analyzer.analyze_image(args.image, args.detailed)
    analyzer.display_results(results, args.format)

if __name__ == "__main__":
    main()
