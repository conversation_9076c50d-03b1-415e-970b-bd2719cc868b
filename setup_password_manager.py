#!/usr/bin/env python3
"""
Setup script for Password Manager & URL Organizer
Checks system requirements and creates desktop shortcuts
"""

import os
import sys
import tkinter as tk
from tkinter import messagebox
import subprocess
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 6):
        return False, f"Python {version.major}.{version.minor}"
    return True, f"Python {version.major}.{version.minor}.{version.micro}"

def check_tkinter():
    """Check if tkinter is available"""
    try:
        import tkinter
        return True, "Available"
    except ImportError:
        return False, "Not available"

def check_required_modules():
    """Check if all required modules are available"""
    required_modules = [
        ('json', 'JSON support'),
        ('base64', 'Base64 encoding'),
        ('webbrowser', 'Web browser integration'),
        ('os', 'Operating system interface'),
        ('pathlib', 'Path handling')
    ]
    
    results = []
    all_available = True
    
    for module, description in required_modules:
        try:
            __import__(module)
            results.append((module, description, True, "Available"))
        except ImportError:
            results.append((module, description, False, "Missing"))
            all_available = False
    
    return all_available, results

def create_desktop_shortcut():
    """Create desktop shortcut (Windows only)"""
    if os.name != 'nt':
        return False, "Not Windows"
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "Password Manager.lnk")
        target = os.path.abspath("password_manager.py")
        wDir = os.path.dirname(target)
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = sys.executable
        shortcut.Arguments = f'"{target}"'
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = sys.executable
        shortcut.save()
        
        return True, path
    except ImportError:
        return False, "winshell not available"
    except Exception as e:
        return False, str(e)

def run_system_check():
    """Run comprehensive system check"""
    print("🔍 Password Manager System Check")
    print("=" * 50)
    
    # Check Python version
    python_ok, python_version = check_python_version()
    print(f"Python Version: {python_version} {'✅' if python_ok else '❌'}")
    
    if not python_ok:
        print("❌ Python 3.6 or higher is required")
        print("💡 Download from: https://python.org")
        return False
    
    # Check tkinter
    tkinter_ok, tkinter_status = check_tkinter()
    print(f"GUI Support (tkinter): {tkinter_status} {'✅' if tkinter_ok else '❌'}")
    
    if not tkinter_ok:
        print("❌ tkinter is required for the GUI")
        print("💡 Install Python with tkinter support")
        return False
    
    # Check required modules
    modules_ok, module_results = check_required_modules()
    print(f"\nRequired Modules:")
    for module, description, available, status in module_results:
        print(f"  {module}: {status} {'✅' if available else '❌'}")
    
    if not modules_ok:
        print("❌ Some required modules are missing")
        return False
    
    # Check if main file exists
    main_file = "password_manager.py"
    if os.path.exists(main_file):
        print(f"\nMain Application: {main_file} ✅")
    else:
        print(f"\nMain Application: {main_file} ❌")
        print("❌ password_manager.py not found in current directory")
        return False
    
    print("\n✅ All system checks passed!")
    return True

def create_test_data():
    """Create sample data for testing"""
    desktop_path = Path.home() / "Desktop"
    
    # Sample password data
    sample_passwords = [
        {
            "title": "Sample Email",
            "username": "<EMAIL>",
            "password": "U2FtcGxlUGFzc3dvcmQxMjM=",  # base64 encoded "SamplePassword123"
            "notes": "This is a sample password entry"
        }
    ]
    
    # Sample URL data
    sample_urls = [
        {
            "title": "Google",
            "url": "https://www.google.com",
            "category": "Search",
            "description": "Google search engine"
        },
        {
            "title": "GitHub",
            "url": "https://github.com",
            "category": "Development",
            "description": "Code repository hosting"
        }
    ]
    
    # Sample settings
    sample_settings = {
        "background_color": "#f0f0f0",
        "theme": "default"
    }
    
    try:
        import json
        
        # Write sample files
        with open(desktop_path / "passwords_data.json", 'w') as f:
            json.dump(sample_passwords, f, indent=2)
        
        with open(desktop_path / "urls_data.json", 'w') as f:
            json.dump(sample_urls, f, indent=2)
        
        with open(desktop_path / "app_settings.json", 'w') as f:
            json.dump(sample_settings, f, indent=2)
        
        return True, "Sample data created on desktop"
    except Exception as e:
        return False, str(e)

def main():
    """Main setup function"""
    print("🔧 Password Manager Setup")
    print("=" * 30)
    
    # Run system check
    if not run_system_check():
        print("\n❌ Setup failed - system requirements not met")
        input("Press Enter to exit...")
        return
    
    print("\n🎯 Setup Options:")
    print("1. Run system check only")
    print("2. Create sample data")
    print("3. Create desktop shortcut (Windows)")
    print("4. Run application")
    print("5. Exit")
    
    while True:
        choice = input("\nEnter choice (1-5): ").strip()
        
        if choice == "1":
            print("✅ System check completed above")
            break
            
        elif choice == "2":
            success, message = create_test_data()
            if success:
                print(f"✅ {message}")
            else:
                print(f"❌ Failed to create sample data: {message}")
            break
            
        elif choice == "3":
            success, message = create_desktop_shortcut()
            if success:
                print(f"✅ Desktop shortcut created: {message}")
            else:
                print(f"❌ Could not create shortcut: {message}")
            break
            
        elif choice == "4":
            print("🚀 Starting Password Manager...")
            try:
                import password_manager
                app = password_manager.PasswordManager()
                app.run()
            except Exception as e:
                print(f"❌ Failed to start application: {e}")
            break
            
        elif choice == "5":
            print("👋 Setup cancelled")
            break
            
        else:
            print("❌ Invalid choice. Please enter 1-5.")
    
    print("\n🎉 Setup completed!")
    input("Press Enter to exit...")

if __name__ == "__main__":
    main()
